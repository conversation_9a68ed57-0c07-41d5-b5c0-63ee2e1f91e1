const path = require("path")
const ForkTsCheckerWebpackPlugin = require("fork-ts-checker-webpack-plugin")

module.exports = {
  target: "web",
  module: {
    rules: [
      {
        type: "javascript/auto",
        test: /\.json$/,
        loader: "json-loader",
      },
      {
        test: /\.(sass|css|scss)$/,
        use: ["style-loader", "css-loader", "postcss-loader", "sass-loader"],
      },
      {
        test: /\.(ts|tsx)/,
        use: [
          {
            loader: "babel-loader",
          },
          {
            loader: "ts-loader",
            options: {
              transpileOnly: true,
            },
          },
        ],
        exclude: /node_modules/,
      },
      {
        test: /\.svg$/,
        use: [
          {
            loader: "@svgr/webpack",
          },
          {
            loader: "file-loader",
            options: {
              name: "static/icons/[name].[hash].[ext]",
            },
          },
        ],
        issuer: {
          and: [/\.(ts|tsx|js|jsx)$/],
        },
      },
      {
        test: /\.(png|jpg|gif|webp)(\?v=[0-9]\.[0-9]\.[0-9])?$/,
        type: "asset/resource",
        generator: {
          filename: "static/images/[name][ext][query]",
        },
      },
      {
        test: /\.(jsx|tsx|ts|js)$/,
        loader: "stylelint-custom-processor-loader",
        exclude: /node_modules/,
      },
    ],
  },
  resolve: {
    extensions: [".tsx", ".ts", ".js", ".jsx", ".scss"],
    alias: {
      utils: path.resolve(__dirname, "./src/utils/"),
      hooks: path.resolve(__dirname, "./src/hooks/"),
      hocs: path.resolve(__dirname, "./src/hocs/"),
      components: path.resolve(__dirname, "./src/components/"),
      assets: path.resolve(__dirname, "./src/assets/"),
      consts: path.resolve(__dirname, "./src/consts/"),
      globalStyles: path.resolve(__dirname, "./src/globalStyles/"),
      routes: path.resolve(__dirname, "./src/routes/"),
    },
  },
  plugins: [
    new ForkTsCheckerWebpackPlugin({
      typescript: {
        diagnosticOptions: {
          semantic: true,
          syntactic: true,
        },
      },
    }),
  ],
}
