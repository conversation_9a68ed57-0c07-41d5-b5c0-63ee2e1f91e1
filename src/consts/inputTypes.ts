import { InputType } from "types/InputTypes"

export const INPUT_TYPES: Record<InputType, InputType> = {
  text: "text",
  textarea: "textarea",
  numeric: "numeric",
  select: "select",
  selectWithSearch: "selectWithSearch",
  checkbox: "checkbox",
  checkboxGroup: "checkboxGroup",
  switch: "switch",
  tag: "tag",
  email: "email",
  radio: "radio",
  radioGroup: "radioGroup",
  combinator: "combinator",
  formatted: "formatted",
  date: "date",
  dateTime: "dateTime",
  dateRange: "dateRange",
  timepicker: "timepicker",
  phone: "phone",
  slider: "slider",
}

export const INPUT_TYPES_LIST: Array<InputType> = Array.from(
  Object.values(INPUT_TYPES)
)
