import React, { useState } from "react"
import { useForm } from "react-hook-form"
import { ComponentMeta, ComponentStory } from "@storybook/react"

import {
  Box,
  Descriptions,
  FormItems as FormItemsComponent,
  FormItemsProps,
  Grid,
  Modal,
  Tabs,
  Typography,
  Button,
  Select,
} from "components"

export default {
  title: "FormItems",
  component: FormItemsComponent,
  parameters: {
    docs: {
      description: {
        component:
          "####Design - [FormItems](https://app.zeplin.io/project/6051ca87147bf646e6cbf660/screen/631b4cd00e5259ad9305fdb0). This Component is wrapper for multiple form items.",
      },
    },
  },
  argTypes: {
    items: {
      defaultValue: [],
      control: "object",
      description: "Array of form items",
    },
    form: {
      defaultValue: null,
      control: "object",
      description: "React Hook Form instance",
    },
    gridItemProps: {
      defaultValue: null,
      control: "object",
      description: "Grid item props",
    },
    gridContainerProps: {
      defaultValue: null,
      control: "object",
      description: "Grid container props",
    },
    validationMessagesMapper: {
      defaultValue: null,
      control: "object",
      description: "Object with validation messages",
    },
  },
} as ComponentMeta<typeof FormItemsComponent>

const Template: ComponentStory<typeof FormItemsComponent> = (
  props: FormItemsProps
) => {
  const form = useForm()

  const defaultItems = [
    {
      type: "text",
      name: "firstName",
      inputProps: {
        label: "First Name",
      },
      gridItemProps: { always: 4 },
    },
    {
      type: "text",
      name: "lastName",
      inputProps: {
        label: "Last Name",
      },
      gridItemProps: { always: 4 },
    },
    {
      type: "select",
      name: "select",
      inputProps: {
        label: "Select",
        options: [
          { value: "1", label: "Option 1" },
          { value: "2", label: "Option 2" },
          { value: "3", label: "Option 3" },
        ],
      },
    },
    {
      type: "empty",
      gridItemProps: { always: 4 },
    },
    {
      type: "action",
      label: "Submit",
      actionProps: {
        variant: "primary",
        type: "submit",
        onClick: form.handleSubmit((data) => {
          console.info(
            `First Name: ${data.firstName} Last Name: ${data.lastName}`
          )
        }),
      },
      gridItemProps: { always: 4 },
    },
  ]

  return <FormItemsComponent form={form} items={defaultItems} {...props} />
}

export const FormItems = Template.bind({})

const items = [...Array(100).keys()].map((i) => ({
  value: i,
  label: `Item ${i}`,
}))

export const FormItemsTypes = () => {
  const form = useForm()

  return (
    <div>
      <Typography variant="--font-headline-3">Item types</Typography>

      <Typography variant="--font-body-text-7">
        There are multiple item types that can be used in the FormItems
        component. The item types are:
      </Typography>
      <Descriptions
        gridColumnTemplates={[{ always: "120px" }, { always: true }]}
        items={[
          { key: 1, label: "text", value: "renders a text input" },
          { key: 2, label: "textarea", value: "renders a text input" },
          { key: 3, label: "select", value: "renders a select input" },
          { key: 4, label: "numeric", value: "renders a numeric input" },
          { key: 5, label: "checkbox", value: "renders a checkbox input" },
          {
            key: 6,
            label: "checkboxGroup",
            value: "renders a checkbox group input",
          },
          { key: 7, label: "radio", value: "renders a radio input" },
          { key: 8, label: "radioGroup", value: "renders a radio group input" },
          { key: 9, label: "switch", value: "renders a switch input" },
          { key: 10, label: "tag", value: "renders a tag input" },
          { key: 11, label: "email", value: "renders a email input" },
          { key: 12, label: "combinator", value: "renders a combinator input" },
          { key: 13, label: "formatted", value: "renders a formatted input" },
          { key: 14, label: "timepicker", value: "renders a timepicker input" },
          { key: 15, label: "empty", value: "renders an empty space" },
          { key: 16, label: "action", value: "renders an action button" },
          {
            key: 17,
            label: "component",
            value: "renders a custom component (ReactNode)",
          },
          {
            key: 18,
            label: "function",
            value: "renders a custom function (which returns a ReactNode)",
          },
        ]}
      />
      <br />
      <Typography variant="--font-body-text-7">
        All the item types are represented below in the same order as above.
        Submit button itself does not fire submit action. To bind submit
        actions, pass a function to the onClick prop of the action item. The
        onSubmit function must be wrapped in handleSubmit() function from
        react-hook-form.
      </Typography>
      <br />
      <Typography variant="--font-body-text-7">
        Also, the form below demonstrates responsive layout. The gridItemProps
        prop can be used to set the responsive layout of the item. The prop
        accepts props of the Grid component.
      </Typography>
      <br />
      <FormItems
        form={form}
        gridContainerProps={{ gap: "m" }}
        items={[
          {
            type: "text",
            name: "text",
            inputProps: {
              label: "Text",
            },
            gridItemProps: { mSM: 12, tb: 4 },
          },
          {
            type: "textarea",
            name: "textarea",
            inputProps: {
              label: "TextArea",
            },
            gridItemProps: { mSM: 12 },
          },
          {
            type: "select",
            name: "select",
            inputProps: {
              label: "Select",
              options: items,
            },
            gridItemProps: { mSM: 12, tb: 4 },
          },
          {
            type: "numeric",
            name: "numeric",
            inputProps: {
              label: "Numeric",
            },
            gridItemProps: { mSM: 12, tb: 4 },
          },
          {
            type: "checkbox",
            name: "checkbox",
            inputProps: {
              label: "Checkbox",
            },
            gridItemProps: { mSM: 12, tb: 4 },
          },
          {
            type: "checkboxGroup",
            name: "checkboxGroup",
            inputProps: {
              label: "Checkbox Group",
              items: items.slice(0, 12),
              gridItemTemplateProps: { mSM: 6, tb: 3 },
            },
            gridItemProps: { mSM: 12 },
          },
          {
            type: "radio",
            name: "radio",
            inputProps: {
              name: "radio",
              label: "Radio",
              value: true,
            },
            gridItemProps: { mSM: 12, tb: 4 },
          },
          {
            type: "radioGroup",
            name: "radioGroup",
            inputProps: {
              name: "radioGroup",
              label: "Radio Group",
              items: items.slice(0, 12),
              gridItemTemplateProps: { mSM: 6, tb: 3 },
            },
            gridItemProps: { mSM: 12 },
          },
          {
            type: "switch",
            name: "switch",
            inputProps: {
              label: "Switch",
            },
            gridItemProps: { mSM: 12, tb: 4 },
          },
          {
            type: "tag",
            name: "tag",
            inputProps: {
              label: "Tag",
            },
            gridItemProps: { mSM: 12, tb: 4 },
          },
          {
            type: "email",
            name: "email",
            inputProps: {
              label: "Email",
              errorMessagesMapper: {
                pattern: "Invalid pattern",
                maxCount: "Max count exceeded",
                validate: "Invalid value",
                duplicate: "Duplicate value",
              },
            },
            gridItemProps: { mSM: 12, tb: 4 },
          },
          {
            type: "combinator",
            name: "combinator",
            inputProps: {
              label: "Combinator",
              defaultValue: "HTML, CSS, Javascript, Typescript, React, Redux",
            },
            rules: {
              required: true,
            },
            gridItemProps: { mSM: 12, tb: 4 },
          },
          {
            type: "formatted",
            name: "formatted",
            inputProps: {
              label: "Formatted (card number)",
              inputType: "cardNumber",
            },
            gridItemProps: { mSM: 12, tb: 4 },
          },
          {
            type: "timepicker",
            name: "timepicker",
            inputProps: {
              label: "TimePickerInput",
            },
            gridItemProps: { mSM: 12, tb: 4 },
          },
          {
            type: "empty",
            gridItemProps: { mSM: 8, tb: false },
          },
          {
            type: "action",
            label: "Submit",
            actionProps: {
              variant: "primary",
              type: "submit",
              onClick: form.handleSubmit((data) => {
                console.info(JSON.stringify(data))
              }),
            },
            gridItemProps: { mSM: 12, tb: 4 },
          },
        ]}
      />
    </div>
  )
}

export const FormItemsValidations = () => {
  const form = useForm()
  const formWithServerValidation = useForm()
  const [errorDisplayType, setErrorDisplayType] = useState("popover")
  const validationMessagesMapper = {
    required: "This field is required",
    min: "Min value is {min}",
    max: "Max value is {max}",
    minLength: "Min length is {minLength}",
    maxLength: "Max length is {maxLength}",
    pattern: "Invalid pattern",
    validate: "Invalid value",
  }

  return (
    <div>
      <Typography variant="--font-headline-3">
        Client side validations
      </Typography>
      <Typography variant="--font-body-text-7">
        Form client-side validation is handled by react-hook-form. Each item has
        a rules prop which accepts the same rules as react-hook-form.
      </Typography>
      <br />
      <Typography variant="--font-body-text-7">
        Prop validationMessagesMapper is used to map the validation messages.
        The variable in curly braces is replaced by the parameter. So, {"{min}"}{" "}
        will be replaced by the minimum value passed in the validation rule. It
        has a default built-in mapper with values listed below:
      </Typography>
      <br />
      <Descriptions
        gridColumnTemplates={[{ always: "120px" }, { always: true }]}
        items={Object.entries(validationMessagesMapper).map(([key, value]) => ({
          key,
          value,
        }))}
      />
      <br />
      <Typography variant="--font-body-text-7">
        Also, if the message is passed directly as a string, it will be used
        instead of the mapper.
      </Typography>
      <br />
      <Typography variant="--font-body-text-7">
        Prop errorDisplayType is used to set the error display type. It can be
        one of the following values: alert, popover, text. The default value is
        popover. Each item can override the error display type by passing the
        prop errorDisplayType to inputProps.
      </Typography>
      <br />
      <Typography variant="--font-body-text-7">
        One of the benefits of react-hook-form is that it can focus on the first
        invalid field when the form is submitted. This is enabled by default.
      </Typography>
      <br />
      <Select
        isFullWidth
        label="Error display type"
        value={errorDisplayType}
        options={[
          { label: "Alert", value: "alert" },
          { label: "Popover", value: "popover" },
          { label: "Text", value: "text" },
        ]}
        onChange={(option) => {
          if (!Array.isArray(option)) {
            setErrorDisplayType(option.value as string)
          }
        }}
      />
      <br />
      <br />
      <FormItems
        errorDisplayType={errorDisplayType}
        form={form}
        gridContainerProps={{ gap: "m" }}
        items={[
          {
            type: "text",
            name: "requiredText",
            inputProps: {
              label: "Required text",
            },
            gridItemProps: { mSM: 12, tb: 4 },
            rules: {
              required: true,
            },
          },
          {
            type: "text",
            name: "requiredTextWithAlertError",
            inputProps: {
              label: "Required text with alert error",
              errorDisplayType: "alert",
            },
            gridItemProps: { mSM: 12, tb: 4 },
            rules: {
              required: true,
            },
          },
          {
            type: "text",
            name: "requiredTextWithCustomMessage",
            inputProps: {
              label: "Required text with custom message",
            },
            gridItemProps: { mSM: 12, tb: 4 },
            rules: {
              required: "Custom message",
            },
          },
          {
            type: "action",
            label: "Submit",
            actionProps: {
              variant: "primary",
              type: "submit",
              onClick: form.handleSubmit((data) => {
                console.info(JSON.stringify(data))
              }),
            },
            gridItemProps: { mSM: 12, tb: 4 },
          },
        ]}
        validationMessagesMapper={{
          required: "This field is required",
        }}
      />
      <br />
      <Typography variant="--font-headline-3">
        Server-side validation
      </Typography>
      <Typography variant="--font-body-text-7">
        The validation is run before submit. If there are any errors, the form
        is not submitted. If you want to get validation handled by server-side,
        do not include the same validations on client side. Once you obtained
        some errors from the server, you can use the setError function from
        react-hook-form to set the errors.
      </Typography>
      <br />
      <FormItems
        errorDisplayType={errorDisplayType}
        form={formWithServerValidation}
        gridContainerProps={{ gap: "m" }}
        items={[
          {
            type: "text",
            name: "textValidatedByServer",
            inputProps: {
              label: "Text validated by server",
            },
            gridItemProps: { mSM: 12, tb: 4 },
          },
          {
            type: "action",
            label: "Submit",
            actionProps: {
              variant: "primary",
              type: "submit",
              onClick: formWithServerValidation.handleSubmit(() => {
                console.info("Submitted to server. Invalid data!")
                formWithServerValidation.setError(
                  "textValidatedByServer",
                  {
                    type: "validate",
                    message: "This field is invalid. Checked by server",
                  },
                  {
                    shouldFocus: true,
                  }
                )
              }),
            },
            gridItemProps: { mSM: 12, tb: 4 },
          },
        ]}
      />
    </div>
  )
}

export const DisabledFields = () => {
  const form = useForm({
    defaultValues: {
      disabledText: "One Piece, Naruto, Bleach",
      disabledNumeric: 777,
      disabledSelect: "onePiece",
      disabledTextArea:
        '"One Piece," "Naruto," and "Bleach" are three iconic manga series known for their extensive length and enduring popularity.',
      disabledTag: ["One Piece", "Bleach", "Naruto"],
      disabledEmail: ["onePiece", "naruto", "bleach"],
      disabledCombinator: "One Piece, Naruto, Bleach",
      disabledCreditCardNumber: "1234 5678 9012 3456",
    },
  })

  return (
    <Box flexDirection="column" gap="l">
      <Typography variant="--font-body-text-5">
        Inputs with InputContainer can accept popover messages showed in
        disabled states.
      </Typography>

      <FormItems
        form={form}
        gridContainerProps={{ gap: "m" }}
        items={[
          {
            type: "text",
            name: "disabledText",
            inputProps: {
              label: "Disabled text",
              isDisabled: true,
              disabledPopoverMessage: "Disabled text input",
            },
            gridItemProps: { mSM: 4 },
          },
          {
            type: "numeric",
            name: "disabledNumeric",
            inputProps: {
              label: "Disabled numeric",
              isDisabled: true,
              disabledPopoverMessage: "Disabled numeric input",
            },
            gridItemProps: { mSM: 4 },
          },
          {
            type: "select",
            name: "disabledSelect",
            inputProps: {
              label: "Disabled select",
              isDisabled: true,
              disabledPopoverMessage: "Disabled select input",
              options: [
                { label: "One Piece", value: "onePiece" },
                { label: "Naruto", value: "naruto" },
                { label: "Bleach", value: "bleach" },
              ],
            },
            gridItemProps: { mSM: 4 },
          },
          {
            type: "textarea",
            name: "disabledTextArea",
            inputProps: {
              label: "Disabled text area",
              isDisabled: true,
              disabledPopoverMessage: "Disabled text area input",
            },
            gridItemProps: { mSM: 12 },
          },
          {
            type: "tag",
            name: "disabledTag",
            inputProps: {
              label: "Disabled tag",
              isDisabled: true,
              disabledPopoverMessage: "Disabled tag input",
            },
            gridItemProps: { mSM: 12 },
          },
          {
            type: "email",
            name: "disabledEmail",
            inputProps: {
              label: "Disabled email",
              isDisabled: true,
              disabledPopoverMessage: "Disabled email input",
            },
            gridItemProps: { mSM: 12 },
          },
          {
            type: "combinator",
            name: "disabledCombinator",
            inputProps: {
              label: "Disabled combinator",
              isDisabled: true,
              disabledPopoverMessage: "Disabled combinator input",
            },
            gridItemProps: { mSM: 12 },
          },
          {
            type: "formatted",
            name: "disabledCreditCardNumber",
            inputProps: {
              label: "Disabled credit card number",
              isDisabled: true,
              disabledPopoverMessage: "Disabled credit card number input",
              inputType: "cardNumber",
            },
            gridItemProps: { mSM: 12 },
          },
          {
            type: "timepicker",
            name: "disabledTimePicker",
            inputProps: {
              label: "Disabled time picker",
              isDisabled: true,
              disabledPopoverMessage: "Disabled time picker input",
            },
            gridItemProps: { mSM: 12 },
          },
        ]}
      />
    </Box>
  )
}

export const ProductFormExample = () => {
  const [errorDisplayType, setErrorDisplayType] =
    useState<FormItemsProps["errorDisplayType"]>("popover")
  const [isModalVisible, setIsModalVisible] = useState(false)
  const form = useForm({ mode: "onSubmit", reValidateMode: "onSubmit" })

  const handleClose = () => {
    form.reset()
    setIsModalVisible(false)
  }

  const handleSubmit = form.handleSubmit((data) => {
    console.info(JSON.stringify(data))
    handleClose()
  })

  return (
    <div>
      <Button
        onClick={() => {
          setIsModalVisible(true)
        }}
      >
        Open Product Form Modal
      </Button>

      <Modal
        cancelButtonText="Cancel"
        okButtonText="Submit"
        title="GigaBlue Quad Plus Linux Satelliten Receiver (HDTV, Full HD, PVR-Ready, 2x CI, CR, LCD, IPTV, HbbTV, Twin Tuner, USB)"
        visible={isModalVisible}
        width="--modal-size-l"
        onCancel={handleClose}
        onOk={handleSubmit}
      >
        <div style={{ margin: -20 }}>
          <Grid container>
            <Grid item always="295px">
              <div style={{ borderRight: "1px solid #d5dce0", height: "100%" }}>
                <FormItemsComponent
                  boxContainerProps={{ display: "block", padding: "l" }}
                  errorDisplayType={errorDisplayType}
                  form={form}
                  gridContainerProps={{ gap: "l" }}
                  items={[
                    {
                      type: "component",
                      component: (
                        <Select
                          isFullWidth
                          label="Error display type"
                          value={errorDisplayType}
                          options={[
                            { label: "Alert", value: "alert" },
                            { label: "Popover", value: "popover" },
                            { label: "Text", value: "text" },
                          ]}
                          onChange={(option) => {
                            if (!Array.isArray(option)) {
                              setErrorDisplayType(
                                option.value as FormItemsProps["errorDisplayType"]
                              )
                            }
                          }}
                        />
                      ),
                      gridItemProps: { mSM: 12 },
                    },
                    {
                      type: "component",
                      component: (
                        <Descriptions
                          gridColumnTemplates={[
                            {
                              always: "100px",
                            },
                            { always: true },
                          ]}
                          items={[
                            { key: 1, label: "SKU:", value: "48902277" },
                            {
                              key: 2,
                              label: "Product ID:",
                              value: "128076",
                            },
                            { key: 3, label: "ASIN", value: "B07P4MMQ3G" },
                            {
                              key: 4,
                              label: "Marketplace",
                              value: "Amazon DE",
                            },
                            { key: 5, label: "Condition:", value: "New" },
                            {
                              key: 6,
                              label: "Fulfillment method:",
                              value: "FBM",
                            },
                            { key: 7, label: "Prime status", value: "No" },
                            {
                              key: 8,
                              label: "Date added:",
                              value: "01.12.2022",
                            },
                          ]}
                        />
                      ),
                      gridItemProps: { mSM: 12 },
                    },
                    {
                      type: "component",
                      component: (
                        <Descriptions
                          titles={[null, "B2C", "B2B"]}
                          gridColumnTemplates={[
                            { always: "100px" },
                            { always: true },
                            { always: true },
                          ]}
                          items={[
                            {
                              key: 1,
                              label: "FBA Program:",
                              value: ["N/A", "N/A"],
                            },
                            { key: 2, label: "SnL:", value: ["No", "No"] },
                            {
                              key: 3,
                              label: "Price:",
                              value: ["44.44", "44.44"],
                            },
                            {
                              key: 4,
                              label: "Lowest price:",
                              value: ["399.00", "399.00"],
                            },
                            {
                              key: 5,
                              label: "Initial price:",
                              value: ["17.90", "17.90"],
                            },
                            {
                              key: 6,
                              label: "Current profit:",
                              value: ["8.99", "8.99"],
                            },
                          ]}
                        />
                      ),
                      gridItemProps: { mSM: 12 },
                    },
                  ]}
                />
                <FormItemsComponent
                  boxContainerProps={{ display: "block", padding: "l" }}
                  errorDisplayType={errorDisplayType}
                  form={form}
                  gridContainerProps={{ gap: "l" }}
                  items={[
                    {
                      type: "component",
                      component: (
                        <Box display="flex" flexDirection="column" gap="m">
                          <Typography variant="--font-body-text-5">
                            Dimensions
                          </Typography>
                          <Typography variant="--font-body-text-7">
                            29.21cm x 10.39cm x 43.41cm x 2580.941g
                          </Typography>
                        </Box>
                      ),
                      gridItemProps: { mSM: 12 },
                    },
                    {
                      type: "textarea",
                      name: "comment",
                      inputProps: {
                        label: "Comment",
                        minRows: 5,
                        isRequired: true,
                      },
                      gridItemProps: { mSM: 12 },
                    },
                  ]}
                />
              </div>
            </Grid>
            <Grid always item>
              <Box display="block" padding="l">
                <FormItemsComponent
                  boxContainerProps={{ paddingBottom: "m" }}
                  errorDisplayType={errorDisplayType}
                  form={form}
                  gridContainerProps={{ gap: "m" }}
                  items={[
                    {
                      type: "select",
                      name: "condition",
                      inputProps: {
                        label: "Condition",
                        options: items,
                        hasSearch: true,
                      },
                      rules: {
                        required: "Custom error message",
                      },
                      gridItemProps: { mSM: 12, tb: 4 },
                    },
                    {
                      type: "select",
                      name: "feeCalculation",
                      inputProps: {
                        label: "Fee calculation",
                        options: items,
                      },
                      gridItemProps: { mSM: 12, tb: 4 },
                      rules: {
                        required: true,
                      },
                    },
                    { type: "empty", gridItemProps: { mSM: 12, tb: 4 } },
                    {
                      type: "numeric",
                      name: "purchasePrice",
                      inputProps: { label: "Net purchase price (%)" },
                      rules: {
                        required: true,
                      },
                      gridItemProps: { mSM: 12, tb: 4 },
                    },
                    {
                      type: "numeric",
                      name: "vat",
                      inputProps: { label: "VAT (%)" },
                      gridItemProps: { mSM: 12, tb: 4 },
                      rules: {
                        required: true,
                      },
                    },
                    { type: "empty", gridItemProps: { mSM: 12, tb: 4 } },
                    {
                      type: "numeric",
                      name: "otherFees",
                      inputProps: { label: "Other fees" },
                      gridItemProps: { mSM: 12, tb: 4 },
                      rules: {
                        required: true,
                      },
                    },
                    {
                      type: "numeric",
                      name: "fbaFees",
                      inputProps: { label: "FBA fees" },
                      gridItemProps: { mSM: 12, tb: 4 },
                      rules: {
                        required: true,
                      },
                    },
                    { type: "empty", gridItemProps: { mSM: 12, tb: 4 } },
                    {
                      type: "numeric",
                      name: "retailPrice",
                      inputProps: { label: "Recommended retail price" },
                      gridItemProps: { mSM: 12, tb: 4 },
                      rules: {
                        required: true,
                      },
                    },
                    {
                      type: "numeric",
                      name: "shipmentFee",
                      inputProps: { label: "Shipment fee" },
                      gridItemProps: { mSM: 12, tb: 4 },
                      rules: {
                        required: true,
                      },
                    },
                    { type: "empty", gridItemProps: { mSM: 12, tb: 4 } },
                    {
                      type: "numeric",
                      name: "formerSellingPrice",
                      inputProps: { label: "Former selling price" },
                      gridItemProps: { mSM: 12, tb: 4 },
                      rules: {
                        required: true,
                      },
                    },
                    { type: "empty", gridItemProps: { mSM: 12, tb: 4 } },
                    { type: "empty", gridItemProps: { mSM: 12, tb: 4 } },
                    {
                      type: "select",
                      name: "productTags",
                      inputProps: {
                        label: "Product tags",
                        options: [
                          {
                            value: "1",
                            label: "Tag name 1",
                            tagColor: "#eb2f96",
                          },
                          {
                            value: "2",
                            label: "Tag name 2",
                            tagColor: "#ea7e00",
                          },
                          {
                            value: "3",
                            label: "Tag name 3",
                            tagColor: "#60b400",
                          },
                          {
                            value: "4",
                            label: "Tag name 4",
                            tagColor: "#1890ff",
                          },
                          {
                            value: "5",
                            label: "Tag name 5",
                            tagColor: "#722ed1",
                          },
                          {
                            value: "6",
                            label: "Tag name 6",
                            tagColor: "#666666",
                          },
                        ],
                        isMultiSelect: true,
                        hasSearch: true,
                      },
                      gridItemProps: { mSM: 12 },
                      rules: {
                        required: true,
                      },
                    },
                  ]}
                />
                <Tabs
                  items={[
                    {
                      key: "1",
                      label: "B2C",
                      contents: (
                        <FormItemsComponent
                          boxContainerProps={{ paddingTop: "m" }}
                          errorDisplayType={errorDisplayType}
                          form={form}
                          gridContainerProps={{ gap: "m" }}
                          items={[
                            {
                              type: "numeric",
                              name: "price",
                              inputProps: {
                                label: "Price",
                              },
                              gridItemProps: { mSM: 12, tb: 4 },
                            },
                            {
                              type: "numeric",
                              name: "sippingPrice",
                              inputProps: {
                                label: "Shipping price",
                              },
                              gridItemProps: { mSM: 12, tb: 4 },
                            },
                            {
                              type: "empty",
                              gridItemProps: { mSM: 12, tb: 4 },
                            },
                            {
                              type: "numeric",
                              name: "fbaFee",
                              inputProps: {
                                label: "FBA fee",
                              },
                              gridItemProps: { mSM: 12, tb: 4 },
                            },
                            {
                              type: "empty",
                              gridItemProps: { mSM: 12, tb: 4 },
                            },
                            {
                              type: "empty",
                              gridItemProps: { mSM: 12, tb: 4 },
                            },
                            {
                              type: "numeric",
                              name: "currentProfit",
                              inputProps: {
                                label: "Current profit",
                              },
                              gridItemProps: { mSM: 12, tb: 4 },
                            },
                            {
                              type: "numeric",
                              name: "thresholdPrice",
                              inputProps: {
                                label: "Threshold price",
                              },
                              gridItemProps: { mSM: 12, tb: 4 },
                            },
                            {
                              type: "empty",
                              gridItemProps: { mSM: 12, tb: 4 },
                            },
                            {
                              type: "text",
                              name: "buyBoxWinner",
                              inputProps: {
                                label: "BuyBox winner",
                              },
                              gridItemProps: { mSM: 12, tb: 4 },
                            },
                            {
                              type: "select",
                              name: "buyBoxEligibility",
                              inputProps: {
                                label: "BuyBox eligibility",
                                options: items,
                              },
                              gridItemProps: { mSM: 12, tb: 4 },
                            },
                            {
                              type: "text",
                              name: "buyBoxShare24h",
                              inputProps: {
                                label: "BuyBox share 24h",
                              },
                              gridItemProps: { mSM: 12, tb: 4 },
                            },
                            {
                              type: "text",
                              name: "buyBoxWinners",
                              inputProps: {
                                label: "BuyBox winners",
                              },
                              gridItemProps: { mSM: 12, tb: 4 },
                            },
                            {
                              type: "select",
                              name: "fbaBuyBoxWinners",
                              inputProps: {
                                label: "FBA BuyBox winners",
                                options: items,
                              },
                              gridItemProps: { mSM: 12, tb: 4 },
                            },
                            {
                              type: "text",
                              name: "fbmBuyBoxWinners",
                              inputProps: {
                                label: "FBM BuyBox winners",
                              },
                              gridItemProps: { mSM: 12, tb: 4 },
                            },
                            {
                              type: "numeric",
                              name: "lowestPrice",
                              inputProps: {
                                label: "Lowest price",
                              },
                              gridItemProps: { mSM: 12, tb: 4 },
                            },
                            {
                              type: "numeric",
                              name: "buyBoxPrice",
                              inputProps: {
                                label: "BuyBox price",
                              },
                              gridItemProps: { mSM: 12, tb: 4 },
                            },
                            {
                              type: "empty",
                              gridItemProps: { mSM: false, tb: 4 },
                            },
                            {
                              type: "numeric",
                              name: "standalonePrice",
                              inputProps: {
                                label: "Standalone price",
                              },
                              gridItemProps: { mSM: 12, tb: 4 },
                            },
                            {
                              type: "empty",
                              gridItemProps: { mSM: false, tb: 8 },
                            },
                            {
                              type: "switch",
                              name: "priceScale",
                              inputProps: {
                                label: "Price scale",
                              },
                              gridItemProps: { always: 12 },
                            },
                            {
                              type: "switch",
                              name: "optimizationActive",
                              inputProps: {
                                label: "Optimization active",
                              },
                              gridItemProps: { mSM: 12, tb: 4 },
                            },
                            {
                              type: "select",
                              name: "optimizationTemplate",
                              inputProps: {
                                label: "Optimization template",
                                options: items,
                              },
                              gridItemProps: { mSM: 12, tb: 4 },
                            },
                            {
                              type: "checkboxGroup",
                              name: "subcondition",
                              inputProps: {
                                label: "Subcondition",
                                gridItemTemplateProps: {
                                  mSM: 12,
                                  tb: 6,
                                  dSM: 3,
                                },
                                items: [
                                  { value: "new", label: "New" },
                                  { value: "warranty", label: "Warranty" },
                                  { value: "oem", label: "OEM" },
                                  { value: "openBox", label: "Open box" },
                                ],
                              },
                              gridItemProps: { mSM: 12, dSM: 8 },
                            },
                            {
                              type: "empty",
                              gridItemProps: { mSM: false, tb: 4 },
                            },
                          ]}
                        />
                      ),
                    },
                    {
                      key: "2",
                      label: "B2B",
                      contents: "B2B",
                    },
                  ]}
                />
              </Box>
            </Grid>
          </Grid>
        </div>
      </Modal>
    </div>
  )
}

export const CreditCardExample = () => {
  const form = useForm()

  return (
    <Box
      flexDirection="column"
      gap="l"
      padding="l"
      style={{
        maxWidth: 984 + 40,
      }}
    >
      <Box
        style={{
          border: "var(--border-main)",
          borderRadius: "var(--border-radius)",
        }}
      >
        <FormItems
          form={form}
          gridContainerProps={{ gap: "m" }}
          boxContainerProps={{
            width: "100%",
            padding: "l",
          }}
          items={[
            {
              type: "text",
              name: "cardholderName",
              inputProps: {
                label: "Cardholder name",
              },
              gridItemProps: {
                always: 12,
              },
            },
            {
              type: "formatted",
              name: "cardNumber",
              inputProps: {
                label: "Card number",
                inputType: "cardNumber",
              },
              gridItemProps: {
                mSM: 12,
                tb: "calc(100% - 380px)",
              },
            },
            {
              type: "formatted",
              name: "cardExpirationDate",
              inputProps: {
                label: "Expire date",
                inputType: "cardExpirationDate",
              },
              gridItemProps: {
                mSM: 6,
                tb: "190px",
              },
            },
            {
              type: "formatted",
              name: "cardSecurityCode",
              inputProps: {
                label: "CVV",
                inputType: "cardSecurityCode",
                isSecured: true,
              },
              gridItemProps: {
                mSM: 6,
                tb: "190px",
              },
            },
            {
              type: "text",
              name: "ZIP",
              inputProps: {
                label: "ZIP",
              },
              gridItemProps: {
                mSM: 12,
                tb: "190px",
              },
            },
          ]}
        />
      </Box>
      <Box gap="m" justify="end">
        <Button variant="secondary">Back</Button>
        <Button
          variant="primary"
          onClick={form.handleSubmit((data) => {
            console.info(JSON.stringify(data))
          })}
        >
          Save
        </Button>
      </Box>
    </Box>
  )
}
