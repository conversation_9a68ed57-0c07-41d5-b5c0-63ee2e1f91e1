import React from "react"
import { ComponentStory } from "@storybook/react"
import {
  Box,
  Checkbox as CheckboxComponent,
  Tag as TagComponent,
  Typography,
} from "components"

export default {
  title: "Checkbox",
  component: CheckboxComponent,
  parameters: {
    docs: {
      description: {
        component:
          "####Design - [Checkbox](https://app.zeplin.io/project/6051cbfe3fed74441799d895/screen/63ea1ac4e05bd51885f92d13)",
      },
    },
  },
  argTypes: {
    color: {
      description: "Custom color of checkbox. Accepts string value of color",
    },
    type: {
      description: "Sets which icon will be displayed in checkbox",
    },
    size: {
      description: "Accepts size",
    },
    isFilled: {
      description:
        "Whether checkbox is filled in both unchecked and checked states. " +
        "This property will be available only with passed color property. " +
        "Property unavailable with 'selectAll' type ",
    },
    defaultChecked: {
      description: "Initial value of checkbox",
    },
    checked: {
      description: "Value of checkbox",
    },
    onChange: {
      description: "Function to be called when checkbox is clicked",
    },
    disabled: {
      description: "Disables checkbox",
    },
    className: {
      description: "Custom class name",
    },
    label: {
      description: "Label of checkbox",
    },
    labelPosition: {
      description:
        "Position of label relative to the checkbox. Accepts 'left' or 'right'",
      defaultValue: "right",
    },
    labelVariant: {
      description:
        "Font variant of label. The same as variant prop in Typography component",
    },
    isLabelClickable: {
      description:
        "Determines whether value of checkbox can be changed by clicking on label",
      defaultValue: true,
    },
    hasEllipsis: {
      description: "Determines whether label has ellipsis when it is too long",
      defaultValue: true,
    },
    hasEllipsisPopover: {
      description: "Determines whether label has popover when it is too long",
      defaultValue: true,
    },
    onPointerEnter: {
      description:
        "Function to be called when pointer enters the checkbox area, excluding the label",
    },
    onPointerLeave: {
      description:
        "Function to be called when pointer leaves the checkbox area, excluding the label",
    },
    onClick: {
      description:
        "Function to be called when checkbox is clicked, excluding the label",
    },
  },
}

const Template: ComponentStory<typeof CheckboxComponent> = (args) => {
  return <CheckboxComponent {...args} />
}
const ShowcaseTemplate: ComponentStory<typeof TagComponent> = () => (
  <>
    <h4>Default type</h4>
    <Box
      align="center"
      justify="space-between"
      style={{ marginBottom: "20px", maxWidth: "150px" }}
    >
      <CheckboxComponent type="default" />
      <CheckboxComponent checked type="default" />
      <CheckboxComponent disabled type="default" />
      <CheckboxComponent checked disabled type="default" />
    </Box>
    <h4>Expand type</h4>
    <Box
      align="center"
      justify="space-between"
      style={{ marginBottom: "20px", maxWidth: "150px" }}
    >
      <CheckboxComponent type="expand" />
      <CheckboxComponent checked type="expand" />
      <CheckboxComponent disabled type="expand" />
      <CheckboxComponent checked disabled type="expand" />
    </Box>
    <h4>Collapse type</h4>
    <Box
      align="center"
      justify="space-between"
      style={{ marginBottom: "20px", maxWidth: "150px" }}
    >
      <CheckboxComponent type="collapse" />
      <CheckboxComponent checked type="collapse" />
      <CheckboxComponent disabled type="collapse" />
      <CheckboxComponent checked disabled type="collapse" />
    </Box>
    <h4>Select all type</h4>
    <Box
      align="center"
      justify="space-between"
      style={{ marginBottom: "20px", maxWidth: "150px" }}
    >
      <CheckboxComponent type="selectAll" />
      <CheckboxComponent checked type="selectAll" />
      <CheckboxComponent disabled type="selectAll" />
      <CheckboxComponent checked disabled type="selectAll" />
    </Box>

    <h4>Custom color</h4>
    <Box
      align="center"
      justify="space-between"
      style={{ marginBottom: "20px", maxWidth: "150px" }}
    >
      <CheckboxComponent color="#920fa2" type="default" />
      <CheckboxComponent checked color="#920fa2" type="default" />
      <CheckboxComponent disabled color="#920fa2" type="default" />
      <CheckboxComponent checked disabled color="#920fa2" type="default" />
    </Box>

    <h4>Custom color and filled</h4>
    <Box
      align="center"
      justify="space-between"
      style={{ marginBottom: "20px", maxWidth: "150px" }}
    >
      <CheckboxComponent isFilled color="#920fa2" type="default" />
      <CheckboxComponent checked isFilled color="#920fa2" type="default" />
      <CheckboxComponent disabled isFilled color="#920fa2" type="default" />
      <CheckboxComponent
        checked
        disabled
        isFilled
        color="#920fa2"
        type="default"
      />
    </Box>

    <h4>Labels</h4>
    <Box
      flexDirection="column"
      gap="m"
      justify="space-between"
      style={{ marginBottom: "20px", maxWidth: "150px" }}
    >
      <CheckboxComponent label="Right" labelPosition="right" />
      <CheckboxComponent label="Left" labelPosition="left" />
      <CheckboxComponent isLabelClickable={false} label="Not clickable" />
      <CheckboxComponent
        label="Different font"
        labelVariant="--font-body-text-6"
      />
      <CheckboxComponent
        label="Different font"
        labelVariant="--font-body-text-11"
      />
    </Box>
  </>
)

export const Checkbox = Template.bind({})
export const Showcase = ShowcaseTemplate.bind({})

Checkbox.args = {
  color: "",
}

export const CheckboxWithLabel = () => {
  return (
    <Box flexDirection="column" gap="l">
      <Typography variant="--font-body-text-7">
        Checkbox label can be placed on both sides of the checkbox element. It
        is controlled by the <b>labelPosition</b> prop. By default, the label is
        placed on the right side of the checkbox.
        <br />
        <br />
        Its font can be customized by the <b>labelVariant</b> prop. It is the
        same as in the Typography component.
        <br />
        <br />
        If the label is too long, it can be truncated with an ellipsis or
        displayed in a popover. This behavior is controlled by the{" "}
        <b>hasEllipsis</b> and <b>hasEllipsisPopover</b> props. By default, the
        label has an ellipsis with a popover.
      </Typography>
      <Box flexDirection="column" gap="l" width={150}>
        <CheckboxComponent label="Right-side label" />
        <CheckboxComponent label="Left-side label" labelPosition="left" />
        <CheckboxComponent label="Right-side label with ellipsis" />
      </Box>
    </Box>
  )
}
