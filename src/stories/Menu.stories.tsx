import React from "react"
import { ComponentMeta, ComponentStory } from "@storybook/react"

import { Menu as MenuComponent, MenuProps } from "components"

const options = [...Array(10).keys()].map((i) => ({
  label: `Option ${i}`,
  value: i,
}))

export default {
  title: "Menu",
  component: MenuComponent,
  parameters: {
    docs: {
      description: {
        component:
          "####Design - [Menus](https://app.zeplin.io/project/6051cbfe3fed74441799d895/screen/606428b1389f5cbff4cc7335)",
      },
    },
  },
  argTypes: {
    isNavigationEnabled: {
      description: "Enable keyboard navigation",
    },
    isSelectByClickEnabled: {
      description: "Enable selection by click",
    },
    isSelectByEnterEnabled: {
      description: "Enable selection by enter",
    },
    isVisible: {
      description:
        "Is menu visible. When visible, keyboard navigation is enabled by default",
    },
    items: {
      description:
        "Menu items. Each item has a label and a value. Also, you can pass any other data in the item object",
      defaultValue: options,
    },
    onSelect: {
      description:
        "Callback function when an item is selected. Receives the selected item index as an argument",
    },
    renderItem: {
      description:
        "Function to render each item. Receives the item object as an argument",
    },
    size: {
      description:
        "Size of the menu item. If not provided, the default size is medium",
    },
    onKeyDown: {
      description:
        "Callback function when a key is pressed. Receives the event object as an argument",
    },
    selectedIndex: {
      description:
        "Index of the selected item. If not provided, the inner state is used",
    },
  },
} as ComponentMeta<typeof MenuComponent>

const Template: ComponentStory<typeof MenuComponent> = (props: MenuProps) => (
  <MenuComponent {...props} />
)

export const Menu = Template.bind({})
