import React from "react"
import { ComponentMeta, ComponentStory } from "@storybook/react"

import { EmptyImage as EmptyImageComponent, EmptyImageProps } from "components"

export default {
  title: "EmptyImage",
  component: EmptyImageComponent,
  parameters: {
    docs: {
      description: {
        component:
          "Image component with load and error handlers and the possibility of adding a fallback image",
      },
    },
  },
  argTypes: {
    url: {
      control: "text",
      description: "Primary image url (src)",
    },
    fallbackUrl: {
      control: "text",
      description:
        "The image that will be loaded if the main image isn't loaded successfully",
    },
    isBlurred: {
      control: "boolean",
      description: "Whether to apply a blur effect to the image",
      defaultValue: false,
    },
    blurRadius: {
      control: "text",
      description: "The blur radius to apply when isBlurred is true",
      defaultValue: "4px",
    },
  },
} as ComponentMeta<typeof EmptyImageComponent>

const Template: ComponentStory<typeof EmptyImageComponent> = ({
  ...props
}: EmptyImageProps) => {
  const asin = "B004W2UC0U"
  const sellerId = "A1CP7QQ915GP38"
  const imageUrl = `https://ws-eu.amazon-adsystem.com/widgets/q?MarketPlace=DE&ASIN=${asin}&ServiceVersion=20070822&ID=AsinImage&WS=1&Format=_SL160_`
  const imageFallbackUrl = `https://images-na.ssl-images-amazon.com/images/P/${asin}.01-${sellerId}.MAIN.jpg`

  return (
    <EmptyImageComponent
      fallbackUrl={imageFallbackUrl}
      url={imageUrl}
      {...props}
    />
  )
}

export const EmptyImage = Template.bind({})

export const BlurredEmptyImage = () => {
  const asin = "B004W2UC0U"
  const sellerId = "A1CP7QQ915GP38"
  const imageUrl = `https://ws-eu.amazon-adsystem.com/widgets/q?MarketPlace=DE&ASIN=${asin}&ServiceVersion=20070822&ID=AsinImage&WS=1&Format=_SL160_`
  const imageFallbackUrl = `https://images-na.ssl-images-amazon.com/images/P/${asin}.01-${sellerId}.MAIN.jpg`

  return (
    <div style={{ display: "flex", flexDirection: "column", gap: "20px" }}>
      <div>
        <h3>Normal Image (No Blur)</h3>
        <EmptyImageComponent fallbackUrl={imageFallbackUrl} url={imageUrl} />
      </div>

      <div>
        <h3>Blurred Image (2px radius)</h3>
        <EmptyImageComponent
          isBlurred
          blurRadius="2px"
          fallbackUrl={imageFallbackUrl}
          url={imageUrl}
        />
      </div>

      <div>
        <h3>Blurred Image (4px radius - default)</h3>
        <EmptyImageComponent
          isBlurred
          fallbackUrl={imageFallbackUrl}
          url={imageUrl}
        />
      </div>

      <div>
        <h3>Blurred Image (8px radius)</h3>
        <EmptyImageComponent
          isBlurred
          blurRadius="8px"
          fallbackUrl={imageFallbackUrl}
          url={imageUrl}
        />
      </div>
    </div>
  )
}
