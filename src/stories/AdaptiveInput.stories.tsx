import React, { useState } from "react"
import { ComponentMeta, ComponentStory } from "@storybook/react"

import {
  AdaptiveInput as AdaptiveInputComponent,
  AdaptiveInputProps,
  Box,
  Switch,
  Typography,
} from "components"
import { INPUT_TYPES } from "../consts"
import { inputItemsWithContainer, inputItemsWithoutContainer } from "./mockData"
import { InputItemWithContainer } from "../types"

export default {
  title: "AdaptiveInput",
  component: AdaptiveInputComponent,
  argTypes: {
    type: {
      description: "Type of input",
      control: {
        type: "radio",
      },
      defaultValue: INPUT_TYPES.text,
    },
    inputProps: {
      description: "Props for the input component",
      control: {
        type: "object",
      },
      defaultValue: {},
    },
  },
} as ComponentMeta<typeof AdaptiveInputComponent>

const Template: ComponentStory<typeof AdaptiveInputComponent> = (
  props: AdaptiveInputProps
) => <AdaptiveInputComponent {...props} />

export const AdaptiveInput = Template.bind({})

export const AdaptiveInputTypes = () => {
  const [hasLongLabel, setHasLongLabel] = useState(false)
  const [hasLabelTooltip, setHasLabelTooltip] = useState(true)
  const [hasValueTooltip, setHasValueTooltip] = useState(false)

  return (
    <Box flexDirection="column" gap="l">
      <Typography variant="--font-body-text-7">
        AdaptiveInput is a component designed for cases when different inputs
        should be displayed based on type.
        <br />
        Two main props are <b>type</b> and <b>inputProps</b>. Type is a string
        indicating the type of input. inputProps is an object with props for the
        input component.
        <br />
        There are also props to handle the states of the input: <b>
          value
        </b>, <b>onChange</b>,<b>onBlur</b> and <b>sorting</b>. They are
        convenient for connecting the adaptive input to a form or when different
        inputs should be displayed for different scenarios. Sorting is available
        only for inputs with container because the sorting icon is displayed
        inside a container.
        <br />
        The last prop is <b>size</b> which is used to set the size of the input.
        <br />
        They can be categorized into inputs based on InputContainer and the ones
        without it. Inputs with container can have addons, error messages, and
        other features. Also, they are used as filters whereas inputs without
        container are used as standalone inputs or inside forms only.
      </Typography>
      <Typography variant="--font-headline-5">Inputs with container</Typography>

      <Switch
        isChecked={hasLongLabel}
        label="Long label"
        onChange={setHasLongLabel}
      />

      <Switch
        isChecked={hasLabelTooltip}
        label="Label tooltip"
        onChange={setHasLabelTooltip}
      />

      <Switch
        isChecked={hasValueTooltip}
        label="Value tooltip"
        onChange={setHasValueTooltip}
      />

      <Box
        hasBorder
        display="grid"
        gap="m"
        gridTemplateColumns="120px minmax(0, 1fr) minmax(0, 1fr) minmax(0, 1fr)"
        padding="l"
      >
        <>
          <Typography variant="--font-body-text-6">Type</Typography>
          <Typography variant="--font-body-text-6">Default</Typography>
          <Typography variant="--font-body-text-6">With addons</Typography>
          <Typography variant="--font-body-text-6">With sorting</Typography>
        </>
        {inputItemsWithContainer.map((input) => {
          let label

          if (input.type !== "phone") {
            // eslint-disable-next-line prefer-destructuring
            label = input.inputProps.label

            if (hasLongLabel) {
              label = Array(10).fill(label).join(" ")
            }
          }

          const props = {
            ...input,
            inputProps: {
              ...input.inputProps,
              label,
              hasLabelTooltip,
              hasValueTooltip,
              renderLabel: ({ isFloating }) => {
                return isFloating ? `Floating ${label}` : label
              },
            },
          } as InputItemWithContainer

          return (
            <>
              <Typography variant="--font-body-text-6">{input.type}</Typography>
              <AdaptiveInputComponent {...props} />
              <AdaptiveInputComponent
                {...props}
                addonAfter="After"
                addonBefore="Before"
              />
              <AdaptiveInputComponent
                {...props}
                sorting={{
                  name: props.type,
                  sortLabels: { asc: "Asc", desc: "Desc" },
                  onSort: (sort) => {
                    console.info(sort)
                  },
                }}
              />
            </>
          )
        })}
      </Box>

      <Typography variant="--font-headline-5">
        Inputs without container
      </Typography>
      <Box
        hasBorder
        display="grid"
        gap="m"
        gridTemplateColumns="120px 1fr"
        padding="l"
      >
        <>
          <Typography variant="--font-body-text-6">Type</Typography>
          <Typography variant="--font-body-text-6">Input</Typography>
        </>
        {inputItemsWithoutContainer.map((input) => {
          return (
            <>
              <Typography variant="--font-body-text-6">{input.type}</Typography>
              <AdaptiveInputComponent {...input} />
            </>
          )
        })}
      </Box>
    </Box>
  )
}
