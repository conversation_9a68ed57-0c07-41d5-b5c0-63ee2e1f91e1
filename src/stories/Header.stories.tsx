import React from "react"
import { ComponentMeta, ComponentStory } from "@storybook/react"

import {
  Header as HeaderComponent,
  Icon,
  Typography,
  HeaderProps,
  Box,
} from "components"
import { NestedMenuItem } from "types"
import { HeaderIconItem } from "components/Header/HeaderTypes"

const languages = [
  {
    action: "changeLanguage",
    actionParams: [1, "en"],
    id: "English",
    image: "gb",
    hasTranslation: false,
  },
  {
    action: "changeLanguage",
    actionParams: [2, "ru"],
    id: "Русский",
    image: "ru",
    hasTranslation: false,
  },
  {
    action: "changeLanguage",
    actionParams: [3, "de"],
    id: "Deutsch",
    image: "de",
    hasTranslation: false,
  },
  {
    action: "changeLanguage",
    actionParams: [4, "fr"],
    id: "Français",
    image: "fr",
    hasTranslation: false,
  },
  {
    action: "changeLanguage",
    actionParams: [5, "cn"],
    id: "中文 (中国)",
    image: "cn",
    hasTranslation: false,
  },
  {
    action: "changeLanguage",
    actionParams: [6, "es"],
    id: "Español",
    image: "es",
    hasTranslation: false,
  },
  {
    action: "changeLanguage",
    actionParams: [7, "pt"],
    id: "Português",
    image: "pt",
    hasTranslation: false,
  },
  {
    action: "changeLanguage",
    actionParams: [8, "it"],
    id: "Italiano",
    image: "it",
    hasTranslation: false,
  },
  {
    action: "changeLanguage",
    actionParams: [9, "sv"],
    id: "Svenska",
    image: "se",
    hasTranslation: false,
  },
  {
    action: "changeLanguage",
    actionParams: [10, "nl"],
    id: "Nederlands",
    image: "nl",
    hasTranslation: false,
  },
  {
    action: "changeLanguage",
    actionParams: [11, "uk"],
    id: "Українська",
    image: "ua",
    hasTranslation: false,
  },
  {
    action: "changeLanguage",
    actionParams: [12, "ja"],
    id: "日本語",
    image: "jp",
    hasTranslation: false,
  },
  {
    action: "changeLanguage",
    actionParams: [13, "tr"],
    id: "Türkçe",
    image: "tr",
    hasTranslation: false,
  },
  {
    action: "changeLanguage",
    actionParams: [14, "pl"],
    id: "Polski",
    image: "pl",
    hasTranslation: false,
  },
  {
    action: "changeLanguage",
    actionParams: [15, "ar"],
    id: "العربية",
    image: "sa",
    hasTranslation: false,
  },
  {
    action: "changeLanguage",
    actionParams: [16, "da"],
    id: "Dansk",
    image: "dk",
    hasTranslation: false,
  },
  {
    action: "changeLanguage",
    actionParams: [17, "no"],
    id: "Norsk",
    image: "no",
    hasTranslation: false,
  },
  {
    action: "changeLanguage",
    actionParams: [18, "fi"],
    id: "Suomi",
    image: "fi",
    hasTranslation: false,
  },
  {
    action: "changeLanguage",
    actionParams: [19, "cs"],
    id: "Čeština",
    image: "cz",
    hasTranslation: false,
  },
  {
    action: "changeLanguage",
    actionParams: [20, "hu"],
    id: "Magyar",
    image: "hu",
    hasTranslation: false,
  },
  {
    action: "changeLanguage",
    actionParams: [21, "el"],
    id: "Ελληνικά",
    image: "gr",
    hasTranslation: false,
  },
  {
    action: "changeLanguage",
    actionParams: [22, "ro"],
    id: "Română",
    image: "ro",
    hasTranslation: false,
  },
  {
    action: "changeLanguage",
    actionParams: [23, "sk"],
    id: "Slovenčina",
    image: "sk",
    hasTranslation: false,
  },
  {
    action: "changeLanguage",
    actionParams: [24, "bg"],
    id: "Български",
    image: "bg",
    hasTranslation: false,
  },
  {
    action: "changeLanguage",
    actionParams: [25, "hr"],
    id: "Hrvatski",
    image: "hr",
    hasTranslation: false,
  },
  {
    action: "changeLanguage",
    actionParams: [26, "sr"],
    id: "Српски",
    image: "rs",
    hasTranslation: false,
  },
  {
    action: "changeLanguage",
    actionParams: [27, "sl"],
    id: "Slovenščina",
    image: "si",
    hasTranslation: false,
  },
  {
    action: "changeLanguage",
    actionParams: [28, "lv"],
    id: "Latviešu",
    image: "lv",
    hasTranslation: false,
  },
  {
    action: "changeLanguage",
    actionParams: [29, "lt"],
    id: "Lietuvių",
    image: "lt",
    hasTranslation: false,
  },
  {
    action: "changeLanguage",
    actionParams: [30, "et"],
    id: "Eesti",
    image: "ee",
    hasTranslation: false,
  },
  {
    action: "changeLanguage",
    actionParams: [31, "hi"],
    id: "हिन्दी",
    image: "in",
    hasTranslation: false,
  },
  {
    action: "changeLanguage",
    actionParams: [32, "ko"],
    id: "한국어",
    image: "kr",
    hasTranslation: false,
  },
]
const profile: Array<NestedMenuItem> = [
  {
    action: null,
    icon: null,
    id: "Profile",
    image: null,
    internal: false,
    items: [],
    showUserDetails: false,
  },
  {
    id: "Not ready",
    items: [
      {
        id: "Available",
        actionParams: ["Available"],
        action: "changeUserStatus",
      },
      {
        id: "Not ready",
        actionParams: ["Not ready"],
        action: "changeUserStatus",
      },
      {
        id: "Call",
        actionParams: ["Call"],
        action: "changeUserStatus",
      },
      {
        id: "Break",
        actionParams: ["Break"],
        action: "changeUserStatus",
      },
      {
        id: "Lunch",
        actionParams: ["Lunch"],
        action: "changeUserStatus",
      },
      {
        id: "Meeting",
        actionParams: ["Meeting"],
        action: "changeUserStatus",
      },
      {
        id: "Training",
        actionParams: ["Training"],
        action: "changeUserStatus",
      },
      {
        id: "Offline",
        actionParams: ["Offline"],
        action: "changeUserStatus",
      },
    ],
  },
  {
    key: "language",
    id: "English",
    image: "gb",
    items: languages,
  },
  {
    action: "logout",
    icon: null,
    id: "Log out",
    image: null,
    internal: false,
    items: [],
    showUserDetails: false,
  },
]

const settings = [
  {
    action: null,
    icon: null,
    id: "Users",
    image: null,
    internal: false,
    items: [],
    showUserDetails: false,
  },
  {
    action: null,
    icon: null,
    id: "Amazon accounts",
    image: null,
    internal: false,
    items: [
      {
        action: null,
        icon: null,
        id: "Amazon seller accounts",
        image: null,
        internal: true,
        items: [],
        showUserDetails: false,
      },
      {
        action: null,
        icon: null,
        id: "Amazon Ads accounts",
        image: null,
        internal: true,
        items: [],
        showUserDetails: false,
      },
    ],
    showUserDetails: false,
  },
  {
    action: null,
    icon: null,
    id: "Subscriptions",
    image: null,
    internal: false,
    items: [],
    showUserDetails: false,
  },
  {
    action: null,
    icon: null,
    id: "Customer payments",
    image: null,
    internal: false,
    items: [
      {
        action: null,
        icon: null,
        id: "Payment settings",
        image: null,
        internal: false,
        items: [],
        showUserDetails: false,
      },
      {
        action: null,
        icon: null,
        id: "Invoices",
        image: null,
        internal: false,
        items: [],
        showUserDetails: false,
      },
    ],
    showUserDetails: false,
    url: null,
  },
  {
    action: null,
    icon: null,
    id: "Privacy",
    image: null,
    internal: false,
    items: [],
    showUserDetails: false,
  },
  {
    action: null,
    icon: null,
    id: " Notification center",
    image: null,
    internal: false,
    items: [],
    showUserDetails: false,
  },
  {
    action: null,
    icon: null,
    id: "API settings",
    image: null,
    internal: false,
    items: [],
    showUserDetails: false,
  },
  {
    action: null,
    icon: null,
    id: "Global VAT settings",
    image: null,
    internal: false,
    items: [],
    showUserDetails: false,
  },
  {
    action: null,
    icon: null,
    id: "Organization settings",
    image: null,
    internal: false,
    items: [],
    showUserDetails: false,
  },
]

const customersOptionsPage1 = Array.from({ length: 10 }, (_, i) => i).map(
  (number) => ({
    value: number,
    label: `(${number}) Demo-Company-${number}`,
  })
)

const selectItems = [
  {
    key: "customerSelect",
    type: "selectWithFilter",
    value: 1,
    displayedValue: "Demo-Company-1",
    options: customersOptionsPage1,
    page: 1,
    pageCount: 19,
    searchValue: "",
    onSearch: () => {},
    onSelect: () => {},
    onPageChange: () => {},
  },
]

const icons: Array<HeaderIconItem> = [
  {
    key: "missedTranslations",
    name: "icnFileAdd",
    badge: 1,
  },
  {
    key: "notes",
    name: "icnFileText",
  },
  {
    key: "help",
    name: "icnQuestionCircle",
    items: [
      {
        action: "new-tab",
        icon: null,
        id: "Contact us",
        image: null,
        internal: false,
        items: [],
        showUserDetails: false,
      },
      {
        action: "new-tab",
        icon: null,
        id: "Knowledge base",
        image: null,
        internal: false,
        items: [],
        showUserDetails: false,
      },
      {
        action: "new-tab",
        icon: null,
        id: "Roadmap",
        image: null,
        internal: false,
        items: [],
        showUserDetails: false,
      },
    ],
  },
  {
    key: "notifications",
    name: "icnNotification",
    badge: 167,
    // action: "displayLostNotifications",
  },
  {
    key: "serviceDeskNotifications",
    name: "icnBell",
    badge: 0,
    menu: <div>Hello</div>,
  },
]

const navigation = [
  {
    action: null,
    icon: null,
    id: "Admin",
    image: null,
    internal: false,
    items: [
      {
        action: null,
        icon: null,
        id: "Customer management",
        image: null,
        internal: false,
        items: [
          {
            action: null,
            icon: null,
            id: "Companies",
            image: null,
            internal: false,
            items: [],
            showUserDetails: false,
          },
          {
            action: null,
            icon: null,
            id: "Users",
            image: null,
            internal: false,
            items: [],
            showUserDetails: false,
          },
          {
            action: null,
            icon: null,
            id: "Roles",
            image: null,
            internal: false,
            items: [],
            showUserDetails: false,
          },
        ],
        showUserDetails: false,
      },
      {
        action: null,
        icon: null,
        id: "Financials",
        image: null,
        internal: false,
        items: [
          {
            action: null,
            icon: null,
            id: "Financial overview",
            image: null,
            internal: false,
            items: [],
            showUserDetails: false,
          },
          {
            action: null,
            icon: null,
            id: "Customer invoices",
            image: null,
            internal: true,
            items: [],
            showUserDetails: false,
          },
          {
            action: null,
            icon: null,
            id: "Customer invoices status",
            image: null,
            internal: true,
            items: [],
            showUserDetails: false,
          },
          {
            action: null,
            icon: null,
            id: "Transaction details",
            image: null,
            internal: false,
            items: [],
            showUserDetails: false,
          },
          {
            action: null,
            icon: null,
            id: "Payment modules",
            image: null,
            internal: true,
            items: [],
            showUserDetails: false,
          },
          {
            action: null,
            icon: null,
            id: "Repricer revenue preview",
            image: null,
            internal: true,
            items: [],
            showUserDetails: false,
          },
          {
            action: null,
            icon: null,
            id: "Business Analytics revenue preview",
            image: null,
            internal: true,
            items: [],
            showUserDetails: false,
          },
          {
            action: null,
            icon: null,
            id: "Monthly invoice summary",
            image: null,
            internal: false,
            items: [],
            showUserDetails: false,
          },
          {
            action: null,
            icon: null,
            id: "Transaction statistics",
            image: null,
            internal: false,
            items: [],
            showUserDetails: false,
          },
          {
            action: null,
            icon: null,
            id: "Case statistics",
            image: null,
            internal: false,
            items: [],
            showUserDetails: false,
          },
        ],
        showUserDetails: false,
        url: "",
      },
      {
        action: null,
        icon: null,
        id: "Mail system",
        image: null,
        internal: false,
        items: [
          {
            action: null,
            icon: null,
            id: "Mail templates",
            image: null,
            internal: false,
            items: [],
            showUserDetails: false,
          },
          {
            action: null,
            icon: null,
            id: "Mail queue",
            image: null,
            internal: false,
            items: [],
            showUserDetails: false,
          },
        ],
        showUserDetails: false,
        url: "",
      },
      {
        action: null,
        icon: null,
        id: "Amazon settings",
        image: null,
        internal: false,
        items: [
          {
            action: null,
            icon: null,
            id: "Amazon zones",
            image: null,
            internal: false,
            items: [],
            showUserDetails: false,
          },
          {
            action: null,
            icon: null,
            id: "Amazon marketplaces",
            image: null,
            internal: false,
            items: [],
            showUserDetails: false,
          },
          {
            action: null,
            icon: null,
            id: "Customer product activity",
            image: null,
            internal: false,
            items: [],
            showUserDetails: false,
          },
          {
            action: null,
            icon: null,
            id: "Admin VAT settings",
            image: null,
            internal: false,
            items: [],
            showUserDetails: false,
          },
        ],
        showUserDetails: false,
        url: "",
      },
      {
        action: null,
        icon: null,
        id: "Lost & Found settings",
        image: null,
        internal: false,
        items: [
          {
            action: null,
            icon: null,
            id: "Amazon case templates",
            image: null,
            internal: false,
            items: [],
            showUserDetails: false,
          },
          {
            action: null,
            icon: null,
            id: "Amazon links",
            image: null,
            internal: true,
            items: [],
            showUserDetails: false,
          },
          {
            action: null,
            icon: null,
            id: "Manage services",
            image: null,
            internal: false,
            items: [],
            showUserDetails: false,
          },
          {
            action: null,
            icon: null,
            id: "All support cases",
            image: null,
            internal: false,
            items: [],
            showUserDetails: false,
          },
          {
            action: null,
            icon: null,
            id: "Cases bulk management",
            image: null,
            internal: false,
            items: [],
            showUserDetails: false,
          },
          {
            action: null,
            icon: null,
            id: "Seller Central accounts ",
            image: null,
            internal: false,
            items: [],
            showUserDetails: false,
          },
          {
            action: null,
            icon: null,
            id: "Seller Central invitations",
            image: null,
            internal: false,
            items: [],
            showUserDetails: false,
          },
        ],
        showUserDetails: false,
        url: "",
      },
      {
        action: null,
        icon: null,
        id: "Localization",
        image: null,
        internal: false,
        items: [
          {
            action: null,
            icon: null,
            id: "Languages",
            image: null,
            internal: false,
            items: [],
            showUserDetails: false,
          },
          {
            action: null,
            icon: null,
            id: "Countries",
            image: null,
            internal: false,
            items: [],
            showUserDetails: false,
          },
          {
            action: null,
            icon: null,
            id: "Translation",
            image: null,
            internal: false,
            items: [],
            showUserDetails: false,
          },
        ],
        showUserDetails: false,
        url: "",
      },
      {
        action: null,
        icon: null,
        id: "Affiliate",
        image: null,
        internal: false,
        items: [
          {
            action: null,
            icon: null,
            id: "Partners",
            image: null,
            internal: false,
            items: [],
            showUserDetails: false,
          },
          {
            action: null,
            icon: null,
            id: "Partner credit notes",
            image: null,
            internal: false,
            items: [],
            showUserDetails: false,
          },
          {
            action: null,
            icon: null,
            id: "Advertising",
            image: null,
            internal: false,
            items: [],
            showUserDetails: false,
          },
          {
            action: null,
            icon: null,
            id: "Promocodes",
            image: null,
            internal: false,
            items: [],
            showUserDetails: false,
          },
        ],
        showUserDetails: false,
      },
      {
        action: null,
        icon: null,
        id: "Navigation management",
        image: null,
        internal: true,
        items: [],
        showUserDetails: false,
      },
      {
        action: null,
        icon: null,
        id: "Auth clients",
        image: null,
        internal: true,
        items: [],
        showUserDetails: false,
      },
      {
        action: null,
        icon: "",
        id: "Activity log",
        image: null,
        internal: false,
        items: [],
        showUserDetails: false,
      },
    ],
    showUserDetails: false,
  },
]

const userModeProps = {
  userLabel: "User",
  adminLabel: "Admin",
  isAdminMode: true,
  isVisible: true,
}

const recommendedLanguagesLocales = ["gb", "de", "es", "fr"]

const languagesOrder = [
  {
    label: "Recommended",
    items: languages.filter((item) =>
      recommendedLanguagesLocales.includes(item.image)
    ),
  },
  {
    label: "Other",
    items: languages.filter(
      (item) => !recommendedLanguagesLocales.includes(item.image)
    ),
  },
]

export default {
  title: "Header",
  component: HeaderComponent,
  parameters: {
    docs: {
      description: {
        component:
          "####Design - [Header](https://app.zeplin.io/project/6051cbfe3fed74441799d895/screen/64b66380c16f502209ad34e8)",
      },
    },
  },
  argTypes: {
    profileTitle: {
      description: "Text appearing in the profile menu - full name of the user",
      defaultValue: "Peter Parker",
    },
    profileSubTitle: {
      description: "Text appearing in the profile menu - company name",
      defaultValue: "Parker Industries",
    },
    logoType: {
      description:
        "Type of the logo. The developer should provide different logo types based on url. " +
        "If not provided, default logo without subtitles will be displayed. " +
        "When the view changes to mobile, the mobile logo will be displayed.",
    },
    homepageIconProps: {
      description: "Props for the homepage drawer icon",
      control: {
        type: "object",
      },
      defaultValue: {
        isVisible: true,
        isOpen: false,
        onToggle: () => {},
      },
    },
    navigation: {
      description:
        "Items for navigation bar. " +
        "When there is not enough space to display all items, the excessive ones are put into dropdown menu in an ellipsis icon.",
      defaultValue: navigation,
    },
    userModeProps: {
      description: "Props for the user mode switcher",
      defaultValue: userModeProps,
    },
    selectItems: {
      description: "Selectable values in the header",
      defaultValue: selectItems,
    },
    icons: {
      description: "Icons in the header",
      defaultValue: icons,
    },
    settings: {
      description: "Settings in the header",
      defaultValue: settings,
    },
    profile: {
      description: "Profile menu items",
      defaultValue: profile,
    },
    languages: {
      description: "Languages by categories",
      defaultValue: languagesOrder,
    },
    languagesDrawerTitle: {
      description: "Title of the languages drawer",
      defaultValue: "Language",
    },
    subHeader: {
      description:
        "Sub header. Appears under the header and is usually serves as a page title",
      defaultValue: (
        <>
          <Icon
            name="icnArrowLeft"
            onClick={() => {
              console.info("BACK")
            }}
          />
          <Typography variant="--font-headline-3">Back</Typography>
        </>
      ),
    },
    anotherUserLabel: {
      description:
        "Indicator that the user is logged in as another user. Full name of the user should be provided",
      defaultValue: "Logged in as Spider-man",
    },
    nestedMenuSettings: {
      description: "Settings for nested dropdown menus",
      defaultValue: {
        titleKey: "id",
        urlKey: "url",
        childrenKey: "items",
      },
    },
    homepage: {
      description: "Homepage drawer content",
      defaultValue: (
        <Box
          backgroundColor="--color-badge-2"
          height="2000px"
          paddingLeft="l"
          paddingRight="l"
          width="100%"
        >
          Main content
        </Box>
      ),
    },
    onAction: {
      description: "Action handler",
      defaultValue: (actionParams) => {
        console.info(actionParams)
      },
    },
    isItemSelected: {
      description: "Function to check if item is selected",
      defaultValue: (url) => {
        return window.location.pathname === url
      },
    },
    navigateTo: {
      description: "Function to navigate to the url",
      defaultValue: (url) => {
        window.location.href = window.location.origin + url
      },
    },
  },
} as ComponentMeta<typeof HeaderComponent>

const Template: ComponentStory<typeof HeaderComponent> = (
  props: HeaderProps
) => {
  return <HeaderComponent {...props} />
}

export const Header = Template.bind({})
