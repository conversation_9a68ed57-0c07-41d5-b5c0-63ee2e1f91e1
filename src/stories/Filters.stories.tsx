import React from "react"
import { ComponentMeta, ComponentStory } from "@storybook/react"

import { Filters as FiltersComponent, FiltersProps } from "components"
import { fieldsWithContainer } from "./mockData"

export default {
  title: "Filters",
  component: FiltersComponent,
  argTypes: {
    boxContainerProps: {
      control: {
        type: "object",
      },
      description:
        "Props for the Box container that wraps the filters form. The same as in the FormItems component.",
    },
    gridContainerProps: {
      control: {
        type: "object",
      },
      description:
        "Props for the Grid container that wraps the filters form. The same as in the FormItems component.",
      defaultValue: {
        gap: "m",
      },
    },
    items: {
      control: {
        type: "object",
      },
      description:
        "Items to render in the filters form. The same as in the FormItems component.",
      defaultValue: fieldsWithContainer,
    },
    defaultValues: {
      control: {
        type: "object",
      },
      description: "Default values for the filters form.",
    },
    onSearch: {
      control: {
        type: "function",
      },
      description: "Function to execute when the form is submitted.",
      defaultValue: ({ params, searchString }) =>
        console.info({ params, searchString }),
    },
    prefix: {
      control: {
        type: "string",
      },
      description: "Prefix is added to the keys of url params.",
      defaultValue: "",
    },
    debounceTime: {
      control: {
        type: "number",
      },
      description: "Debounce time for the search function.",
      defaultValue: 500,
    },
    isConnectedToUrl: {
      control: {
        type: "boolean",
      },
      description: "Indicates if the filters are connected to the URL.",
      defaultValue: true,
    },
    updateUrlSearch: {
      control: {
        type: "function",
      },
      description:
        "Function to update the URL search string. By default, document.location.search is updated.",
    },
    parseFilterValuesFromUrl: {
      control: {
        type: "function",
      },
      description:
        "Function to parse the filter values from URL params. It is executed before resetting the form values" +
        " to the values obtained from url. It is needed because values from url are always strings and we need to" +
        " convert them to the correct type.",
    },
    normalizeFilterValuesToUrl: {
      control: {
        type: "function",
      },
      description:
        "Function to normalize the filter values to URL params. It is executed before updating the URL search" +
        " string. It is needed to convert values like dates, date ranges, etc. to strings.",
    },
  },
} as ComponentMeta<typeof FiltersComponent>

const Template: ComponentStory<typeof FiltersComponent> = (
  props: FiltersProps<any>
) => <FiltersComponent {...props} />

export const Filters = Template.bind({})
