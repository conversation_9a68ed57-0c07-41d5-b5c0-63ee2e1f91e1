import React from "react"
import { ComponentMeta, ComponentStory } from "@storybook/react"
import * as icons from "assets/icons/index"

import { Box, IconPopover as IconPopoverComponent } from "components"

const iconsNames = Object.keys(icons)

const defaultProps = {
  title: "Title",
  content: "Content",
}

export default {
  title: "IconPopover",
  component: IconPopoverComponent,
  parameters: {
    docs: {
      description: {
        component:
          "####Design - [Icon set](https://app.zeplin.io/project/6051cbfe3fed74441799d895/screen/60c341c1f69cc117cab58a54)",
      },
    },
  },
  argTypes: {
    trigger: {
      defaultValue: "hover",
      options: ["hover", "click", "focus"],
      control: { type: "radio" },
    },
    title: { defaultValue: defaultProps.title, control: "text" },
    content: { defaultValue: defaultProps.content, control: "text" },
    isDarkMode: {
      defaultValue: false,
      control: "boolean",
    },
    hasArrow: {
      defaultValue: true,
      control: "boolean",
    },
    size: {
      description: "Takes a variable name",
    },
    color: {
      description: "Takes a variable name",
    },
    stroke: {
      description: "Takes a variable name",
    },
  },
} as ComponentMeta<typeof IconPopoverComponent>

const iconSizes = {
  "--icon-size-1": 12,
  "--icon-size-2": 14,
  "--icon-size-3": 16,
  "--icon-size-4": 18,
  "--icon-size-5": 20,
  "--icon-size-6": 24,
  "--icon-size-7": 32,
  "--icon-size-8": 40,
  "--icon-size-9": 48,
  "--icon-size-10": 56,
  "--icon-size-11": 64,
}

const Template: ComponentStory<typeof IconPopoverComponent> = (args) => {
  const { size = "--icon-size-6" } = args

  const containerWidth = iconSizes[size || "--icon-size-6"] * 3
  const containerHeight = iconSizes[size || "--icon-size-6"] * 3

  return (
    <Box flexWrap="wrap" gap="l">
      {iconsNames.map((iconName) => {
        return (
          <Box
            key={iconName}
            hasBorder
            align="center"
            borderRadius="--border-radius"
            flexDirection="column"
            gap="s"
            height={containerHeight}
            justify="center"
            padding="s 0 0"
            width={containerWidth}
          >
            <Box align="center" flex={1} justify="center">
              <IconPopoverComponent name={iconName} {...args} />
            </Box>
            <span
              title={`${iconName}: Click to copy to clipboard`}
              style={{
                background: "#f3f3f3",
                padding: "1px 5px",
                marginTop: "auto",
                fontSize: 12,
                zIndex: 1,
                cursor: "pointer",
                width: "100%",
                textOverflow: "ellipsis",
                overflow: "hidden",
                whiteSpace: "nowrap",
              }}
            >
              {iconName}
            </span>
          </Box>
        )
      })}
    </Box>
  )
}

export const IconPopover = Template.bind({})

IconPopover.parameters = {
  docs: {
    source: {
      code: '<Icon name="icnAlert" />\n<Icon name="icnAlert" size="--icon-size-4" />\n<Icon name="icnCamera" color="--color-icon-active" />\n<Icon name="icnSpinner" stroke="--color-icon-active" />',
    },
  },
}

IconPopover.args = {
  color: null,
  size: null,
}
