import React, { forwardRef } from "react"

import { Clickable<PERSON>abelBox, I<PERSON>, Spinner } from "components"

import { useSwitch } from "./hooks"

import { StyledSwitch } from "./StyledSwitch"

import { ModeProps, SwitchProps } from "./SwitchTypes"

export const Switch = forwardRef<HTMLDivElement, SwitchProps>(
  (
    {
      label,
      labelPosition = "right",
      labelVariant = "--font-body-text-7",
      size = "m",
      hasStatusIcon,
      isDisabled = false,
      isCheckedDefault = false,
      isChecked: isCheckedProp,
      isLoading = false,
      onChange,
      align = "center",
      isNumeric = false,
      hasEllipsis,
      hasEllipsisPopover,
      onPointerEnter,
      onPointerLeave,
    },
    ref
  ) => {
    const { isChecked, handleClick } = useSwitch({
      isDisabled,
      ...({
        isCheckedDefault,
        isChecked: isCheckedProp,
        onChange,
        isNumeric,
      } as ModeProps),
    })

    return (
      <ClickableLabelBox
        align={align}
        hasEllipsis={hasEllipsis}
        hasEllipsisPopover={hasEllipsisPopover}
        isClickable={false}
        label={label}
        position={labelPosition}
        variant={labelVariant}
      >
        <StyledSwitch
          ref={ref}
          hasStatusIcon={hasStatusIcon}
          isChecked={isChecked}
          isDisabled={isDisabled}
          size={size}
          onClick={handleClick}
          onPointerEnter={onPointerEnter}
          onPointerLeave={onPointerLeave}
        >
          <div data-component-type="mark">
            {isLoading ? <Spinner size="sm" type="circle" /> : null}
          </div>
          {hasStatusIcon ? (
            <div data-component-type="status-icon">
              <Icon
                color="--color-icon-white"
                name={isChecked ? "icnCheck" : "icnClose"}
                size="--icon-size-1"
              />
            </div>
          ) : null}
        </StyledSwitch>
      </ClickableLabelBox>
    )
  }
)
