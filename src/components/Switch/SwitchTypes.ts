import { HTMLAttributes } from "react"

import { BoxProps, ClickableLabelBoxProps } from "components"

type DefaultModeProps = {
  isNumeric?: false
  isCheckedDefault?: boolean
  isChecked?: boolean
  onChange?: (isChecked: boolean) => void
}

type NumericModeProps = {
  isNumeric: true
  isCheckedDefault?: number
  isChecked?: number
  onChange?: (isChecked: number) => void
}

export type ModeProps = DefaultModeProps | NumericModeProps

export type SwitchProps = {
  label?: ClickableLabelBoxProps["label"]
  labelPosition?: ClickableLabelBoxProps["position"]
  labelVariant?: ClickableLabelBoxProps["variant"]
  size?: "m" | "s"
  hasStatusIcon?: boolean
  isDisabled?: boolean
  isLoading?: boolean
  align?: BoxProps["align"]
  hasEllipsis?: boolean
  hasEllipsisPopover?: boolean
} & ModeProps &
  Pick<HTMLAttributes<HTMLDivElement>, "onPointerLeave" | "onPointerEnter">

export type StyledSwitchProps = Pick<
  SwitchProps,
  "size" | "hasStatusIcon" | "isDisabled" | "isChecked" | "isLoading"
>
