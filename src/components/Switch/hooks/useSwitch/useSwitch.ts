import { useEffect, useState } from "react"

import { checkIsFunction } from "utils"

import { UseSwitch } from "./UseSwitchTypes"

export const useSwitch: UseSwitch = ({
  isCheckedDefault,
  isChecked: isCheckedProp,
  isDisabled,
  onChange,
  isNumeric,
}) => {
  const [isChecked, setIsChecked] = useState(
    Boolean(isCheckedDefault || isCheckedProp)
  )

  useEffect(() => {
    if (isCheckedProp === undefined) {
      return
    }

    setIsChecked(Boolean(isCheckedProp))
  }, [isCheckedProp])

  const handleClick = () => {
    if (isDisabled) {
      return
    }

    const isCheckedNew = !isChecked

    if (isCheckedProp === undefined) {
      setIsChecked((prevState) => !prevState)
    }
    if (!checkIsFunction(onChange)) {
      return
    }
    if (isNumeric === true) {
      onChange(isCheckedNew ? 1 : 0)
    } else {
      onChange(isCheckedNew)
    }
  }

  return {
    isChecked,
    handleClick,
  }
}
