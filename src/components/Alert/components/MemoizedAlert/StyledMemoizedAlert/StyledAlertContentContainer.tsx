import styled from "styled-components"

import { StyledAlertContentContainerProps } from "components/Alert/AlertTypes"
import { CLOSE_ICON_MARGIN } from "components/Alert/constants"
import { Typography } from "components/Typography"

export const StyledAlertContentContainer = styled.div<StyledAlertContentContainerProps>`
  flex-grow: 100;
  display: flex;
  flex-direction: row;
  gap: var(--gap-m);
  margin-right: ${({ isMobileView, isContentFullWidth, isClosable }) => {
    if (isClosable && (isMobileView || isContentFullWidth)) {
      return CLOSE_ICON_MARGIN
    }

    return 0
  }}px;

  &[data-align-center="true"] {
    align-items: center;
  }

  > div[data-content-type="icon"] {
    height: var(--icon-size-5);
  }

  > div[data-content-type="text"] {
    display: flex;
    flex-direction: column;
    gap: var(--gap-s);
  }
`

export const StyledText = styled(Typography)`
  word-break: break-word;

  a {
    color: var(--color-text-link);
  }
`
