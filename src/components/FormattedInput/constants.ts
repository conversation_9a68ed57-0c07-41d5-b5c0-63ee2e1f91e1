import { FormattedInputType } from "./FormattedInputTypes"

export const masks: Record<FormattedInputType, string> = {
  cardExpirationDate: "MM/YY",
  cardNumber: "0000 0000 0000 0000",
  cardSecurityCode: "000",
}

export const autoCompletes: Record<FormattedInputType, string> = {
  cardExpirationDate: "cc-exp",
  cardNumber: "cc-number",
  cardSecurityCode: "cc-csc",
}

export const separators: Record<FormattedInputType, RegExp> = {
  cardExpirationDate: /[^0-9]/g,
  cardNumber: /[^0-9]/g,
  cardSecurityCode: /[^0-9]/g,
}
