import { ChangeEvent } from "react"

import { FormattedInputProps, InputContainerProps } from "components"

type UseFormattedInputParams = {
  isFocused
  isSecured
} & Pick<
  FormattedInputProps,
  | "inputType"
  | "valueMode"
  | "value"
  | "defaultValue"
  | "onChange"
  | "suffixIcons"
>

type UseFormattedInputReturn = {
  value: string
  formattedValue: string
  handleChange: (event: ChangeEvent<HTMLInputElement>) => void
  placeholderRemainder: string
  securedValue: string
  autoComplete?: string
  isFilled: InputContainerProps["isFilled"]
} & Pick<FormattedInputProps, "suffixIcons">

export type UseFormattedInput = (
  params: UseFormattedInputParams
) => UseFormattedInputReturn
