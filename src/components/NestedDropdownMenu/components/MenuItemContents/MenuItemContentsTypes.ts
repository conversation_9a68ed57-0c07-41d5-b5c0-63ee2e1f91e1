import { HTMLAttributes, ReactNode } from "react"

export type MenuItemContentsProps = {
  children: ReactNode
  hasChildren: boolean
  isRoot: boolean
  isActive: boolean
  isSelected: boolean
  isDisabled: boolean
} & Pick<
  HTMLAttributes<HTMLLIElement>,
  "onClick" | "onPointerEnter" | "onPointerLeave"
>

export type StyledMenuItemContentsProps = Omit<
  MenuItemContentsProps,
  "hasChildren" | "rootId"
>
