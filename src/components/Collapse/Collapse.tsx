import React from "react"

import { CollapseItem } from "./components"
import { useCollapse } from "./hooks"

import { StyledCollapse } from "./StyledCollapse"

import { CollapseProps } from "./CollapseTypes"

export const Collapse = ({
  items,
  activeKeys: activeKeysProp,
  defaultActiveKeys = [],
  variant = "default",
  headerPadding = "l",
  headerGap = "l",
  chevronSize = "--icon-size-5",
  isContentAlignedWithArrowBox = true,
  isAccordion = false,
  isEventPropagationStopped = true,
  hasChevron = true,
  hasLeftAndRightBorders = false,
  hasTopAndBottomBorders = true,
  onChange,
}: CollapseProps) => {
  const { activeKeys, buildToggle } = useCollapse({
    defaultActiveKeys,
    activeKeys: activeKeysProp,
    isAccordion,
    onChange,
    items,
  })

  return (
    <StyledCollapse
      chevronSize={chevronSize}
      hasChevron={hasChevron}
      hasLeftAndRightBorders={hasLeftAndRightBorders}
      hasTopAndBottomBorders={hasTopAndBottomBorders}
      headerGap={headerGap}
      headerPadding={headerPadding}
      isContentAlignedWithArrowBox={isContentAlignedWithArrowBox}
      isEventPropagationStopped={isEventPropagationStopped}
      variant={variant}
    >
      {items?.map((item) => {
        const isExpanded = activeKeys.has(item.key)

        return (
          <CollapseItem
            key={item.key}
            buildToggle={buildToggle}
            chevronSize={chevronSize}
            hasChevron={hasChevron}
            isEventPropagationStopped={isEventPropagationStopped}
            isExpanded={isExpanded}
            item={item}
          />
        )
      })}
    </StyledCollapse>
  )
}
