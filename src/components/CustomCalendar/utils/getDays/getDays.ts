import {
  addDays,
  eachDayOfInterval,
  eachWeekOfInterval,
  endOfMonth,
  startOfMonth,
} from "date-fns"

import { GetDays } from "./GetDaysTypes"

export const getDays: GetDays = ({ locale, currentMonth, weekStartsOn }) => {
  const startOfMonthDate = startOfMonth(currentMonth)
  const endOfMonthDate = endOfMonth(currentMonth)

  const monthWeeks = eachWeekOfInterval(
    { start: startOfMonthDate, end: endOfMonthDate },
    { locale, weekStartsOn }
  )

  if (monthWeeks.length === 5) {
    monthWeeks.push(addDays(monthWeeks[monthWeeks.length - 1], 7))
  }

  return monthWeeks.flatMap((weekStartDate) =>
    eachDayOfInterval({
      start: weekStartDate,
      end: addDays(weekStartDate, 6),
    })
  )
}
