import React, { useContext, useMemo } from "react"

import { getYears } from "components/CustomCalendar/utils"

import { ConfigContext } from "../ConfigProvider"
import { CurrentMonthContext } from "../CurrentMonthProvider"
import { MonthsAndYearsGrid } from "../MonthsAndYearsGrid"
import { Year } from "../Year"

export const Years = ({ selected }) => {
  const { currentMonth } = useContext(CurrentMonthContext)

  const { locale } = useContext(ConfigContext)

  const years = useMemo(
    () => getYears({ locale, currentMonth }),
    [locale, currentMonth]
  )

  return (
    <MonthsAndYearsGrid>
      {years.map(({ date, title }) => (
        <Year key={title} date={date} selected={selected}>
          {title}
        </Year>
      ))}
    </MonthsAndYearsGrid>
  )
}
