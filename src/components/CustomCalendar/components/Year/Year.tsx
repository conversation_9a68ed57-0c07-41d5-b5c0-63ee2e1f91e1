import React, { useCallback, useContext, useMemo } from "react"

import { getCellStates } from "components/CustomCalendar/utils"

import { ConfigContext } from "../ConfigProvider"
import { CurrentMonthContext } from "../CurrentMonthProvider"
import { NavModeContext } from "../NavModeProvider"
import { StyledCell } from "../StyledCell"

import { YearProps } from "./YearTypes"

export const Year = ({ children, date, selected }: YearProps) => {
  const { moveToMonth } = useContext(CurrentMonthContext)

  const { onChangeToMonths } = useContext(NavModeContext)

  const { fromDate, toDate } = useContext(ConfigContext)

  const { isSelected, isInSelection, isLastHighlighted, isDisabled } =
    useMemo(() => {
      return getCellStates({
        selected,
        date,
        fromDate,
        toDate,
        type: "year",
        locale: null,
        weekStartsOn: null,
      })
    }, [selected, date, fromDate, toDate])

  const handleClick = useCallback(
    (event) => {
      event.stopPropagation()

      moveToMonth(date)
      onChangeToMonths()
    },
    [date, moveToMonth, onChangeToMonths]
  )

  return (
    <StyledCell
      data-is-highlighted={isInSelection}
      data-is-last-highlighted={isLastHighlighted}
      data-is-selected={isSelected}
      disabled={isDisabled}
      type="button"
      onClick={handleClick}
    >
      {children}
    </StyledCell>
  )
}
