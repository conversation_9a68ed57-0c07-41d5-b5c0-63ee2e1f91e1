import React, { useMemo, useState } from "react"

import { NAV_MODES } from "components/CustomCalendar/constants"

import { NavModeContext } from "./NavModeContext"

import { NavMode } from "./NavModeProviderTypes"

export const NavModeProvider = ({ children }) => {
  const [navMode, setNavMode] = useState<NavMode>(NAV_MODES.days)

  const value = useMemo(() => {
    const handleChangeToDays = () => {
      setNavMode(NAV_MODES.days)
    }

    const handleChangeToMonths = () => {
      setNavMode(NAV_MODES.months)
    }

    const handleChangeToYears = () => {
      setNavMode(NAV_MODES.years)
    }

    return {
      navMode,
      onChangeToDays: handleChangeToDays,
      onChangeToMonths: handleChangeToMonths,
      onChangeToYears: handleChangeToYears,
    }
  }, [navMode])

  return (
    <NavModeContext.Provider value={value}>{children}</NavModeContext.Provider>
  )
}
