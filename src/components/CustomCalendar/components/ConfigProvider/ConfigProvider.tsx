import React, { useMemo } from "react"

import { getFromToDates } from "utils"

import { ConfigContext } from "./ConfigContext"

import { ConfigContextType, ConfigProviderProps } from "./ConfigProviderTypes"

export const ConfigProvider = ({
  children,
  locale,
  weekStartsOn,
  fromDate,
  toDate,
  fromMonth,
  toMonth,
  fromYear,
  toYear,
}: ConfigProviderProps) => {
  const value = useMemo<ConfigContextType>(() => {
    return {
      locale,
      weekStartsOn,
      ...getFromToDates({
        fromDate,
        fromMonth,
        fromYear,
        toDate,
        toMonth,
        toYear,
      }),
    }
  }, [
    locale,
    weekStartsOn,
    fromDate,
    toDate,
    fromMonth,
    toMonth,
    fromYear,
    toYear,
  ])

  return (
    <ConfigContext.Provider value={value}>{children}</ConfigContext.Provider>
  )
}
