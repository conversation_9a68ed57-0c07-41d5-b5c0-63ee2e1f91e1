import styled from "styled-components"

export const StyledCell = styled.button`
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  border: none;
  background-color: transparent;
  cursor: pointer;
  border-radius: var(--border-radius);
  outline: none;
  color: var(--color-text-main);

  :disabled {
    color: var(--color-text-disable);
    cursor: not-allowed;
  }

  &[data-is-selected="true"] {
    border: var(--border-button-primary);
    color: var(--color-text-link);
  }

  &[data-is-highlighted="true"] {
    ::after {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      bottom: 0;
      right: 0;
      background-color: var(--color-row-select);
      z-index: -1;
    }

    :not(&[data-is-last-highlighted="true"]) {
      ::after {
        right: calc(-1 * var(--gap-m) - 1px);
      }
    }

    :not(:hover, :focus-visible, &[data-is-selected="true"]) {
      ::after {
        top: 1px;
        bottom: 1px;
      }
    }
  }

  :hover:not(:disabled),
  :focus-visible {
    border: var(--border-main);
  }
`
