import React from "react"

import { <PERSON><PERSON>, Icon, Popover } from "components"
import { checkIsFunction } from "utils"

import { StyledSidebarPanelTab } from "./StyledSidebarPanelTab"

import { SidebarPanelTabProps } from "./SidebarPanelTabTypes"

export const SidebarPanelTab = ({
  activeTabId,
  tab,
  onChange,
  onNavigateTo,
  onSetActiveTabId,
}: SidebarPanelTabProps) => {
  const isTabActive: boolean = activeTabId === tab.id

  const handleTabClick = (): void => {
    if (tab.url) {
      onNavigateTo(tab.url)

      return
    }

    if (isTabActive) {
      onSetActiveTabId("")
    } else {
      onSetActiveTabId(tab.id)
    }

    if (checkIsFunction(onChange)) {
      onChange(tab.id)
    }
  }

  return (
    <Popover
      content={tab?.disabledPopoverContent}
      placement={tab?.disabledPopoverPlacement}
    >
      <StyledSidebarPanelTab
        data-active={isTabActive}
        data-disabled={tab.isDisabled}
        onClick={!tab.isDisabled ? handleTabClick : undefined}
      >
        <div data-content-type="icon">
          {tab.badgeProps ? (
            <Badge {...tab.badgeProps}>
              <Icon name={tab.iconName} />
            </Badge>
          ) : (
            <Icon name={tab.iconName} />
          )}
        </div>
        <div data-content-type="label">{tab.label}</div>
      </StyledSidebarPanelTab>
    </Popover>
  )
}
