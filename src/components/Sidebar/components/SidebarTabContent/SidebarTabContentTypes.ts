import { ReactElement } from "react"

import { SidebarPanelTabType } from "../SidebarPanel/components/SidebarPanelTab"

import { SidebarPanelSideType } from "../SidebarPanel/SidebarPanelTypes"

type SidebarTabContentChildrenProps = {
  activeTab: SidebarPanelTabType
  onCloseTab: () => void
}

export type SidebarTabContentChildrenType = (
  props: SidebarTabContentChildrenProps
) => ReactElement

export type SidebarTabContentProps = {
  children: SidebarTabContentChildrenType
  activeTab: SidebarPanelTabType | null
  tabSide: SidebarPanelSideType | undefined
  sides: SidebarPanelSideType[] | undefined
  onCloseTab?: () => void
}

export type StyledSidebarTabContentBuildSideStylesProps = {
  side: SidebarPanelSideType
}
