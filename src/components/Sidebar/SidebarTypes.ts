import { SidebarPanelSideType, SidebarPanelTabType } from "./components"

import { SidebarTabContentChildrenType } from "./components/SidebarTabContent/SidebarTabContentTypes"

export type SidebarTabsType = Partial<
  Record<SidebarPanelSideType, SidebarPanelTabType[]>
>

export type SidebarNavigateToType = (url: string) => void

export type SidebarProps = {
  activeTabId?: string
  children?: SidebarTabContentChildrenType
  tabs?: SidebarTabsType
  shouldOutsideClickClose?: boolean
  onChange?: (tabId: string) => void
  onNavigateTo?: SidebarNavigateToType
}
