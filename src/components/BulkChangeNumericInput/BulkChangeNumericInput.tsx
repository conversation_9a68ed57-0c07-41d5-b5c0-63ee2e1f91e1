import React, { forwardRef } from "react"

import { InputWithDropdownContainer, NumericInput } from "components"
import { useBulkChange } from "hooks/useBulkChange/useBulkChange"

import type { BulkChangeNumericInputProps } from "./BulkChangeNumericInputTypes"

import {
  NumberModeProps,
  StringModeProps,
} from "../NumericInput/NumericInputTypes"

export const BulkChangeNumericInput = forwardRef<
  HTMLInputElement,
  BulkChangeNumericInputProps
>(
  (
    {
      hasNoChangesMode = true,
      hasEditMode = true,
      hasClearMode = true,
      labels,
      modeValues,
      modeDisplayValues,
      selectedModeValue: selectedModeValueProp,
      onSelectMode,
      isStringMode,
      value: valueProp,
      defaultValue,
      displayedValue: displayedValueProp,
      onChange,
      onBlur,
      onFocus,
      onPressEnter,
      ...numericInputProps
    },
    ref
  ) => {
    const {
      modes,
      selectedModeValue,
      handleSelectMode,
      value,
      displayedValue,
      handleChange,
    } = useBulkChange<string | number>({
      hasNoChangesMode,
      hasEditMode,
      hasClearMode,
      labels,
      selectedModeValue: selectedModeValueProp,
      onSelectMode,
      value: valueProp,
      displayedValue: displayedValueProp,
      onChange,
    })

    if (isStringMode) {
      const stringModeProps = {
        isStringMode: true,
        value,
        defaultValue,
        onChange: handleChange,
        onBlur,
        onFocus,
        onPressEnter,
      } as StringModeProps

      return (
        <InputWithDropdownContainer
          modes={modes}
          selectedModeValue={selectedModeValue}
          onSelectMode={handleSelectMode}
        >
          <NumericInput
            {...numericInputProps}
            {...stringModeProps}
            displayedValue={displayedValue}
          />
        </InputWithDropdownContainer>
      )
    }

    const stringModeProps = {
      isStringMode: false,
      value,
      defaultValue,
      onChange: handleChange,
      onBlur,
      onFocus,
      onPressEnter,
    } as NumberModeProps

    return (
      <InputWithDropdownContainer
        modes={modes}
        selectedModeValue={selectedModeValue}
        onSelectMode={handleSelectMode}
      >
        <NumericInput
          ref={ref}
          {...numericInputProps}
          {...stringModeProps}
          displayedValue={displayedValue}
        />
      </InputWithDropdownContainer>
    )
  }
)
