import { useMemo } from "react"

import { getRatioFromValue } from "components/Slider/utils"
import { convertToDimension } from "utils"

import type { UseTracks } from "./UseTracksTypes"

export const useTracks: UseTracks = ({
  min,
  max,
  minEnabled,
  maxEnabled,
  value,
  width,
}) => {
  return useMemo(() => {
    const disabledTrackLeftWidth =
      width *
      getRatioFromValue({
        value: minEnabled,
        min,
        max,
      })

    const disabledTrackRightWidth =
      width *
      getRatioFromValue({
        value: max - maxEnabled,
        min,
        max,
      })

    let activeTrackLeft = 0
    let activeTrackWidth = 0

    if (value.length === 1) {
      const startValue = minEnabled >= 0 ? minEnabled : 0
      const startValueRatio = getRatioFromValue({
        value: startValue,
        min,
        max,
      })

      activeTrackLeft = width * startValueRatio

      const endValue =
        maxEnabled >= 0 ? Math.min(value[0], maxEnabled) : value[0]
      const endValueRatio = getRatioFromValue({
        value: endValue,
        min,
        max,
      })

      activeTrackWidth = width * endValueRatio - activeTrackLeft
    } else {
      activeTrackLeft =
        width *
        getRatioFromValue({
          value: value[0],
          min,
          max,
        })
      activeTrackWidth =
        width *
        getRatioFromValue({
          value: value[1] - value[0],
          min,
          max,
        })
    }

    return {
      activeTrackStyles: {
        left: convertToDimension(activeTrackLeft),
        width: convertToDimension(activeTrackWidth),
      },
      disabledLeftTrackStyles: {
        left: 0,
        width: disabledTrackLeftWidth,
      },
      disabledRightTrackStyles: {
        left: width - disabledTrackRightWidth,
        width: disabledTrackRightWidth,
      },
    }
  }, [min, max, minEnabled, maxEnabled, value, width])
}
