import type {
  <PERSON><PERSON><PERSON>,
  Focus<PERSON>vent<PERSON><PERSON><PERSON>,
  MutableRefObject,
  PointerEventHandler,
  SetStateAction,
} from "react"

import type {
  SliderInternalValue,
  SliderMarkWithPosition,
  SliderProps,
  SliderValue,
} from "components/Slider/SliderTypes"

export type ThumbsParams = {
  index: number | null
  position: number | null
}

export type UseThumbsParams<ValueType extends SliderValue> = Pick<
  SliderProps<ValueType>,
  | "min"
  | "max"
  | "minEnabled"
  | "maxEnabled"
  | "isDisabled"
  | "isSwapEnabled"
  | "onChange"
  | "onChangeValue"
  | "onSelect"
  | "onSelectValue"
> & {
  ref: MutableRefObject<HTMLDivElement>
  width: number
  setValue: Dispatch<SetStateAction<SliderInternalValue>>
  marks: Array<SliderMarkWithPosition>
}

export type UseThumbsReturn = {
  focusedThumbIndex: number | null
  buildHandlePointerDown: (index: number) => PointerEventHandler<HTMLDivElement>
  buildHandleFocusThumb: (index: number) => FocusEventHandler<HTMLDivElement>
  handleBlur: FocusEventHandler<HTMLDivElement>
}

export type UpdateValueParams = {
  index: number
  getThumbValue: (value: SliderInternalValue) => number
  equalCallback?: (value: SliderInternalValue) => void
  changeCallback?: (value: SliderInternalValue) => void
}
