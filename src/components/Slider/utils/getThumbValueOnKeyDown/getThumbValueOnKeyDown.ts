import { GetThumbValueOnKeyDown } from "components/Slider/utils/getThumbValueOnKeyDown/GetThumbValueOnKeyDownTypes"

export const getThumbValueOnKeyDown: GetThumbValueOnKeyDown = ({
  thumbValue,
  marks,
  isIncrement,
}) => {
  const thumbValueIndex = marks.findIndex((mark) => mark.value === thumbValue)

  const thumbValueNewIndex = isIncrement
    ? thumbValueIndex + 1
    : thumbValueIndex - 1

  if (thumbValueNewIndex < 0) {
    return marks[0].value
  }

  if (thumbValueNewIndex >= marks.length) {
    return marks[marks.length - 1].value
  }

  return marks[thumbValueNewIndex].value
}
