import { SizesTypes } from "types"

import { GridProps } from "../Grid"
import { RadioProps } from "../Radio"

type Item = Pick<RadioProps, "label" | "value" | "isDisabled">

type PickedProps = Pick<
  RadioProps,
  "labelPosition" | "labelVariant" | "isLabelClickable" | "onChange"
>

export type RadioGroupProps = {
  name: string
  items: Array<Item>
  label?: string
  gridItemTemplateProps?: GridProps
  selectedValue?: RadioProps["value"]
  defaultSelectedValue?: RadioProps["value"]
  gap?: SizesTypes
  isDisabled?: boolean
} & Partial<PickedProps>
