import React, {
  Children,
  cloneElement,
  forwardRef,
  isValidElement,
} from "react"

import {
  type CombinatorInputProps,
  type SelectProps,
  type TagInputProps,
  CombinatorInput,
  Select,
  TagInput,
} from "components"

import { StyledInputGroup } from "./StyledInputGroup"

import { InputGroupProps } from "./InputGroupTypes"

export const InputGroup = forwardRef<HTMLDivElement, InputGroupProps>(
  (
    { children, size = "medium", isFullWidth, columns, elementType, ...props },
    ref
  ) => {
    return (
      <StyledInputGroup
        ref={ref}
        columns={columns}
        data-component-type="inputGroup"
        data-element-type={elementType}
        isFullWidth={isFullWidth}
        size={size}
        {...props}
      >
        {Children.map(children, (child) => {
          const isSelect =
            isValidElement(child) &&
            typeof child.type !== "string" &&
            child.type === Select

          if (isSelect) {
            return cloneElement(
              child,
              (child.props as SelectProps).isMultiSelect
                ? ({ tagsMode: "count" } as SelectProps)
                : {}
            )
          }

          const isTagInput =
            isValidElement(child) &&
            typeof child.type !== "string" &&
            child.type === TagInput

          if (isTagInput) {
            return cloneElement(child, { isCountMode: true } as TagInputProps)
          }

          const isCombinatorInput =
            isValidElement(child) &&
            typeof child.type !== "string" &&
            child.type === CombinatorInput

          if (isCombinatorInput) {
            return cloneElement(child, {
              isSingleLine: true,
            } as CombinatorInputProps)
          }

          return child
        })}
      </StyledInputGroup>
    )
  }
)
