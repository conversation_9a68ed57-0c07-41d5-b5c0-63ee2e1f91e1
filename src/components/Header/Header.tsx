import React, { use<PERSON><PERSON>back, useEffect, useMemo, useRef, useState } from "react"

import { <PERSON>er, Lo<PERSON> } from "components"
import { useResizeObserver, useToggle } from "hooks"
import { checkIsArray } from "utils"

import {
  AnotherUserLabel,
  HomePageIcon,
  IconItem,
  LanguageMenu,
  NavigationBar,
  ProfileNestedDropdownMenu,
  SelectWrapper,
  SettingsNestedDropdownMenu,
  SideMenu,
  UserMode,
} from "./components"
import { getViewMode, getVisibility } from "./utils"
import { VIEW_MODES } from "./constants"

import { SelectItem } from "./components/SelectItem"
import { StyledHeader } from "./StyledHeader"

import { HeaderProps } from "./HeaderTypes"

export const Header = ({
  // User main info
  profileTitle,
  profileSubTitle,
  // Logo props
  logoType,
  // Homepage icon props
  homepageIconProps,
  // Navigation props
  navigation,
  // User mode props
  userModeProps,
  // Select items
  selectItems,
  // Icon items
  icons,
  // Settings
  settings,
  // Profile
  profile,
  languages,
  languagesDrawerTitle,
  subHeader,
  anotherUserLabel,
  // Nested props
  nestedMenuSettings,
  // Homepage
  homepage,
  // Action props
  onAction,
  // Url related functions
  isItemSelected,
  navigateTo = (url) => {
    window.location.href = window.location.origin + url
  },
}: HeaderProps) => {
  const headerRef = useRef(null)

  const [viewMode, setViewMode] = useState(getViewMode(window.innerWidth))
  const [headerHeight, setHeaderHeight] = useState(60)

  const {
    open: isLanguagesDrawerOpen,
    handleClose: handleCloseLanguagesDrawer,
    handleOpen: handleOpenLanguagesDrawer,
  } = useToggle(false)

  const handleResize = useCallback(() => {
    setViewMode(getViewMode(window.innerWidth))

    if (headerRef.current?.offsetHeight > 0) {
      setHeaderHeight(headerRef.current.offsetHeight)
    }
  }, [])

  useResizeObserver({
    ref: headerRef,
    callback: handleResize,
    isEnabled: true,
  })

  const selectedLanguageLocale = useMemo(() => {
    return profile.find((item) => item.key === "language")?.image
  }, [profile])

  const logoTypeProcessed = viewMode === VIEW_MODES.MOBILE ? "mobile" : logoType
  const iconItemBadgeSize = viewMode === VIEW_MODES.MOBILE ? "xs" : "s"

  const {
    isNavigationBarVisible,
    isAnotherUserLabelInSubHeader,
    isToolbarVisible,
    isProfileIconVisible,
    isSettingsIconVisible,
  } = getVisibility({
    viewMode,
    anotherUserLabel,
  })

  useEffect(() => {
    if (!isProfileIconVisible) {
      handleCloseLanguagesDrawer()
    }
  }, [isProfileIconVisible])

  const isUserModeVisible = userModeProps.isVisible && isToolbarVisible

  const isSideMenuVisible =
    (!isNavigationBarVisible && checkIsArray(navigation)) ||
    (!isToolbarVisible &&
      (checkIsArray(selectItems) || userModeProps.isVisible)) ||
    !isProfileIconVisible ||
    !isSettingsIconVisible

  return (
    <>
      <StyledHeader ref={headerRef} viewMode={viewMode}>
        <div data-component-type="main">
          <div data-side="left">
            <Logo type={logoTypeProcessed} />

            <HomePageIcon {...homepageIconProps} />

            {isNavigationBarVisible ? (
              <NavigationBar
                isItemSelected={isItemSelected}
                items={navigation}
                {...nestedMenuSettings}
              />
            ) : null}
          </div>
          <div data-side="right">
            <AnotherUserLabel
              isVisible={!isAnotherUserLabelInSubHeader}
              label={anotherUserLabel}
            />

            {isToolbarVisible ? (
              <SelectWrapper>
                {selectItems.map(({ key, ...item }) => {
                  return <SelectItem key={key} {...item} />
                })}
              </SelectWrapper>
            ) : null}

            <UserMode
              {...userModeProps}
              isPopover
              isVisible={isUserModeVisible}
            />

            {icons.map((icon) => {
              return (
                <IconItem
                  key={icon.key}
                  {...icon}
                  badgeSize={iconItemBadgeSize}
                  isItemSelected={isItemSelected}
                  nestedMenuSettings={nestedMenuSettings}
                  onAction={onAction}
                />
              )
            })}

            {isSettingsIconVisible ? (
              <SettingsNestedDropdownMenu
                isItemSelected={isItemSelected}
                nestedMenuSettings={nestedMenuSettings}
                settings={settings}
                onAction={onAction}
              />
            ) : null}

            {isProfileIconVisible ? (
              <ProfileNestedDropdownMenu
                isItemSelected={isItemSelected}
                nestedMenuSettings={nestedMenuSettings}
                profile={profile}
                profileSubTitle={profileSubTitle}
                profileTitle={profileTitle}
                onAction={onAction}
                onOpenLanguagesDrawer={handleOpenLanguagesDrawer}
              />
            ) : null}

            {checkIsArray(languages) ? (
              <Drawer
                isOpen={isLanguagesDrawerOpen}
                title={languagesDrawerTitle}
                width="m"
                zIndex={100001}
                onClose={handleCloseLanguagesDrawer}
              >
                <LanguageMenu
                  languages={languages}
                  selectedLanguageLocale={selectedLanguageLocale}
                  type="full"
                  onAction={onAction}
                  onClose={handleCloseLanguagesDrawer}
                />
              </Drawer>
            ) : null}

            <SideMenu
              isItemSelected={isItemSelected}
              isProfileVisible={!isProfileIconVisible}
              isSettingsVisible={!isSettingsIconVisible}
              isToolbarVisible={!isToolbarVisible}
              isVisible={isSideMenuVisible}
              languages={languages}
              navigateTo={navigateTo}
              navigation={navigation}
              nestedMenuSettings={nestedMenuSettings}
              profile={profile}
              profileSubTitle={profileSubTitle}
              profileTitle={profileTitle}
              selectItems={selectItems}
              settings={settings}
              userModeProps={userModeProps}
              onAction={onAction}
            />
          </div>
        </div>
        <AnotherUserLabel
          isFullWidth
          isVisible={isAnotherUserLabelInSubHeader}
          label={anotherUserLabel}
        />
        {subHeader ? <div data-component-type="sub">{subHeader}</div> : null}
      </StyledHeader>
      <Drawer
        hasAnimation={false}
        hasBackdrop={false}
        hasPadding={false}
        height={`calc(100vh - ${headerHeight}px)`}
        isOpen={homepageIconProps.isOpen}
        top={headerHeight}
        width="100%"
        zIndex={1000}
      >
        {homepage}
      </Drawer>
      <div
        style={{
          height: headerHeight,
        }}
      />
    </>
  )
}
