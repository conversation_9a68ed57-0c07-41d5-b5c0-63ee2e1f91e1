import React from "react"

import { NavigationBarMenuItem } from "components/Header/components"
import { NestedDropdownMenu } from "components/NestedDropdownMenu"

import type { SettingsNestedDropdownMenuParams } from "./SettingsNestedDropdownMenuTypes"

export const SettingsNestedDropdownMenu = ({
  nestedMenuSettings,
  isItemSelected,
  settings,
  onAction,
}: SettingsNestedDropdownMenuParams) => {
  return (
    <NestedDropdownMenu
      {...nestedMenuSettings}
      isItemSelected={isItemSelected}
      items={settings}
      placement="bottomRight"
      rootIcon="icnSetting"
      side="left"
      subMenuTrigger="hover"
      trigger="hover"
      renderItem={({ item: nestedMenuItem }) => {
        return (
          <NavigationBarMenuItem
            item={nestedMenuItem}
            onAction={onAction}
            {...nestedMenuSettings}
          />
        )
      }}
    />
  )
}
