import React from "react"

import { Icon } from "components"

import { HomePageIconProps } from "./HomePageIconTypes"

export const HomePageIcon = ({
  isVisible,
  isOpen,
  onToggle,
}: HomePageIconProps) => {
  if (!isVisible) {
    return null
  }

  const color = isOpen ? "--color-icon-active" : "--color-icon-clickable"

  return (
    <Icon
      color={color}
      name="icnAppStore"
      size="--icon-size-6"
      onClick={onToggle}
    />
  )
}
