import styled from "styled-components"

import { deviceWithMinWidth } from "globalStyles"

import { getStyleProps } from "./utils/getStylesProps"

import { SkeletonNoCountProps } from "./SkeletonTypes"

const Keyframes = styled.span`
  @keyframes wave {
    0% {
      left: -100%;
    }

    50% {
      left: 0;
    }

    100% {
      left: 100%;
    }
  }
`

export const StyledSkeletonContainer = styled(Keyframes)<SkeletonNoCountProps>(
  (props): any => {
    const {
      isCircle,
      width,
      height,
      isInline,
      animationName,
      animationDuration,
      backgroundColor,
      highlightColor,
      headWidth,
      mSM,
      mMD,
      mLG,
      mXL,
      tb,
      dSM,
      dMD,
      dLG,
      dXL,
    } = props

    return {
      display: "inline-flex",
      overflow: "hidden",
      position: "relative",
      lineHeight: 1,
      minWidth: "1em",
      userSelect: "none",
      pointerEvents: "none",

      ...getStyleProps({
        props: {
          animationName,
          animationDuration,
          backgroundColor,
          highlightColor,
          width,
          height,
          isCircle,
          isInline,
          headWidth,
        },
        originalProps: props,
      }),

      "&:not(:last-child)": {
        marginRight: isInline ? "var(--margin-m)" : "0",
      },

      "&::before": {
        content: '""',
        position: "absolute",
        top: 0,
        left: 0,
        height: "100%",
      },

      [deviceWithMinWidth.mobileS]: getStyleProps({
        props: mSM,
        originalProps: props,
      }),
      [deviceWithMinWidth.mobileM]: getStyleProps({
        props: mMD,
        originalProps: props,
      }),
      [deviceWithMinWidth.mobileL]: getStyleProps({
        props: mLG,
        originalProps: props,
      }),
      [deviceWithMinWidth.mobileXL]: getStyleProps({
        props: mXL,
        originalProps: props,
      }),
      [deviceWithMinWidth.tablet]: getStyleProps({
        props: tb,
        originalProps: props,
      }),
      [deviceWithMinWidth.desktopS]: getStyleProps({
        props: dSM,
        originalProps: props,
      }),
      [deviceWithMinWidth.desktopM]: getStyleProps({
        props: dMD,
        originalProps: props,
      }),
      [deviceWithMinWidth.desktopXL]: getStyleProps({
        props: dXL,
        originalProps: props,
      }),
      [deviceWithMinWidth.desktopL]: getStyleProps({
        props: dLG,
        originalProps: props,
      }),
    }
  }
)
