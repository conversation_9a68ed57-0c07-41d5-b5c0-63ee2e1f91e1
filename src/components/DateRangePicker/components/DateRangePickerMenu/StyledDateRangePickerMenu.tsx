import styled from "styled-components"

import { mainScreenSizes } from "globalStyles"

const { mediaMobileXL } = mainScreenSizes

export const StyledDateRangePickerMenu = styled.div`
  width: min-content;
  display: flex;
  flex-direction: row;

  > div[data-component-type="inputModes"] {
    width: min-content;
  }

  > div[data-component-type="calendar"] {
    display: flex;
    flex-direction: column;
    width: min-content;

    > div[data-component-type="inputs"],
    > div[data-component-type="buttons"] {
      display: flex;
      flex-direction: row;
      gap: var(--gap-m);
      padding: var(--padding-m);
      width: 100%;

      > * {
        flex: 1;
      }
    }

    > div[data-component-type="buttons"] {
      border-top: var(--border-main);
    }
  }

  @media (max-width: ${mediaMobileXL}) {
    width: max-content;
    flex-direction: column;

    > div {
      min-width: 100%;

      &[data-component-type="inputModes"] {
        border: 0;
        padding: var(--padding-m);
        padding-bottom: 0;
      }
    }
  }

  @media (min-width: ${mediaMobileXL}) {
    :has(div[data-component-type="inputModes"]) {
      width: 500px;

      > div[data-component-type="inputModes"] {
        flex-grow: 1;
        border-right: var(--border-main);
        position: relative;
      }
    }
  }
`
