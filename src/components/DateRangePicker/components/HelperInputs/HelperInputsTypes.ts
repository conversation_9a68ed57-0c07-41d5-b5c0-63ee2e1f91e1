import type {
  DateRangePickerProps,
  InputMode,
} from "components/DateRangePicker/DateRangePickerTypes"
import { DateParsingErrorMessages, DateRangeFieldKey } from "types"

export type HelperInputsProps = {
  selected: Interval
  inputMode: InputMode
  activeInputKey: DateRangeFieldKey
  onSelect: (date: Interval) => void
  onChangeActiveInputKey: (key: string) => void
  parsingErrorMessages: DateParsingErrorMessages
} & Pick<
  DateRangePickerProps,
  "locale" | "fromDate" | "toDate" | "labels" | "language"
>
