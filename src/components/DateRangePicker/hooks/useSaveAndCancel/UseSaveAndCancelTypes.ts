import type { DateRangePickerMenuProps } from "components/DateRangePicker/components/DateRangePickerMenu/DateRangePickerMenuTypes"

type UseSaveAndCancelParams = {
  hasInputModes: boolean
  selectedLocal: Interval
  hasBothDates: boolean
} & Pick<DateRangePickerMenuProps, "onSelect" | "onClose" | "inputMode">

type UseSaveAndCancelReturn = {
  onSave: () => void
}

export type UseSaveAndCancel = (
  params: UseSaveAndCancelParams
) => UseSaveAndCancelReturn
