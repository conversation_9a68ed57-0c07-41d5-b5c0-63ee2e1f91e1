import React, {
  Children,
  cloneElement,
  forwardRef,
  isValidElement,
  ReactElement,
  ReactNode,
  useCallback,
  useImperativeHandle,
  useMemo,
  useState,
} from "react"
import enGB from "date-fns/locale/en-GB"

import { DateRangeInput, Dropdown, InputContainerProps } from "components"
import { useControlledVisibility } from "hooks"
import { checkIsFunction, getCurrentTimezone, getFromToDates } from "utils"
import { MAX_YEAR_DEFAULT, MIN_YEAR_DEFAULT } from "consts"
import { ReactElementWithRef } from "types"

import { DateRangePickerMenu } from "./components"
import { INPUT_MODES, LABELS_DEFAULT } from "./constants"

import type { DateRangePickerProps, InputMode } from "./DateRangePickerTypes"

export const DateRangePicker = forwardRef<HTMLDivElement, DateRangePickerProps>(
  (
    {
      name,
      // Locale defines how the value is displayed to the user. Default: eZnGB.
      locale = enGB,
      // Language defines in what language the names for weekdays and months are displayed. Default: enGB.
      language = enGB,
      // Timezone is only displayed, but not involved in any calculations. Default: the current timezone.
      hasTimezoneLabel = false,
      timezone: timezoneProp,
      timezonePopoverContent,
      // Controlled from/to dates
      fromDate: fromDateProp,
      fromMonth,
      fromYear = MIN_YEAR_DEFAULT,
      toDate: toDateProp,
      toMonth,
      toYear = MAX_YEAR_DEFAULT,
      // Defines if the component has input modes. Default: true.
      // Without input modes, the selected state is changed on every change of the input.
      // With input modes, the selected state is changed only when the user clicks the "Save" button and
      // is cleared when the user clicks the "Clear" button.
      hasInputModes = true,
      // Controlled states: inputMode, selected
      inputMode: inputModeProp,
      selected: selectedProp,
      onSelect,
      isClearIconVisible = true,
      // Additional preset options
      presetOptions,
      // Dropdown props
      placement,
      isVisible: isVisibleProp,
      isGlobal,
      onVisibilityChange,
      // InputContainer props
      label,
      renderLabel,
      size,
      isDisabled,
      isValid,
      isFullWidth,
      isRequired,
      onClear,
      prefixIcons,
      suffixIcons,
      errorMessage,
      errorDisplayType,
      errorPopoverPlacement,
      disabledPopoverMessage,
      disabledPopoverPlacement,
      hasLabelTooltip,
      hasValueTooltip,
      // Labels
      labels: labelsProp,
      children,
      presetsOrder,
      onPointerEnter,
      onPointerLeave,
      parsingErrorMessages,
    },
    ref
  ) => {
    const { fromDate, toDate } = useMemo(() => {
      return getFromToDates({
        fromDate: fromDateProp,
        fromMonth,
        fromYear,
        toDate: toDateProp,
        toMonth,
        toYear,
      })
    }, [fromDateProp, fromMonth, fromYear, toDateProp, toMonth, toYear])

    const labels = useMemo(() => {
      const cancel: string =
        labelsProp?.cancel ?? labelsProp?.clear ?? LABELS_DEFAULT.cancel

      return { ...LABELS_DEFAULT, ...labelsProp, cancel }
    }, [labelsProp])

    const timezone = timezoneProp || getCurrentTimezone()

    const { inputRef, menuRef, open, handleClose, handleOpen } =
      useControlledVisibility<HTMLDivElement>({
        isVisible: isVisibleProp,
        outsideClickTriggerEvent: "mousedown",
      })

    useImperativeHandle(ref, () => inputRef?.current)

    const [selected, setSelected] = useState<Interval>(null)

    const [inputMode, setInputMode] = useState<InputMode>(null)

    const handleSetNull = useCallback(() => {
      if (selectedProp === undefined) {
        setSelected(null)
      }

      if (inputModeProp === undefined) {
        setInputMode(INPUT_MODES.between)
      }

      if (checkIsFunction(onSelect)) {
        onSelect({
          selected: null,
          inputMode: INPUT_MODES.between,
        })
      }
    }, [onSelect, selectedProp])

    const handleClear: InputContainerProps["onClear"] = useCallback(
      (event) => {
        handleSetNull()
        if (checkIsFunction(onClear)) {
          onClear(event)
        }
      },
      [onClear, handleSetNull]
    )

    const handleSelect = useCallback(
      (item) => {
        // If no item is selected, we need to clear the selected state
        if (!item) {
          handleSetNull()

          return
        }

        const { selected: selectedNew, inputMode: inputModeNew } = item

        if (selectedProp === undefined) {
          setSelected(selectedNew)
        }

        if (inputModeProp === undefined) {
          setInputMode(inputModeNew)
        }

        if (checkIsFunction(onSelect)) {
          onSelect({
            selected: selectedNew,
            inputMode: inputModeNew,
          })
        }
      },
      [onSelect, selectedProp, inputModeProp]
    )

    const selectedProcessed =
      selectedProp !== undefined ? selectedProp : selected

    const inputModeProcessed =
      inputModeProp !== undefined ? inputModeProp : inputMode

    const childrenMerged = useMemo(() => {
      if (!children) {
        return null
      }

      const child: ReactNode = children ? Children.only(children) : null

      const isValidChild = isValidElement(child)

      if (!isValidChild) {
        return null
      }

      const clonedRef = (node) => {
        const outerRef = (child as ReactElementWithRef).ref

        // const isMutableObjectRef: boolean = outerRef && "current" in outerRef
        if (outerRef && "current" in outerRef) {
          outerRef.current = node
        }
        if (typeof outerRef === "function") {
          outerRef(node)
        }
        inputRef.current = node
      }

      const childrenWithMergedProps = cloneElement(child as ReactElement<any>, {
        ref: clonedRef,
        onMouseDown: (event: MouseEvent) => {
          handleOpen()

          // Separating the expression below to a constant results in ts error
          if (
            isValidElement(children) &&
            checkIsFunction(children.props.onMouseDown)
          ) {
            children.props.onMouseDown(event)
          }
        },
      })

      return childrenWithMergedProps
    }, [])

    if (childrenMerged) {
      return (
        <Dropdown
          isGlobal={isGlobal}
          isVisible={open}
          placement={placement}
          menu={
            <DateRangePickerMenu
              ref={menuRef}
              fromDate={fromDate}
              hasInputModes={hasInputModes}
              hasTimezoneLabel={hasTimezoneLabel}
              inputMode={inputModeProcessed}
              isVisible={open}
              labels={labels}
              language={language}
              locale={locale}
              parsingErrorMessages={parsingErrorMessages}
              presetOptions={presetOptions}
              presetsOrder={presetsOrder}
              selected={selectedProcessed}
              timezone={timezone}
              timezonePopoverContent={timezonePopoverContent}
              toDate={toDate}
              onClose={handleClose}
              onSelect={handleSelect}
            />
          }
          onVisibilityChange={onVisibilityChange}
        >
          {childrenMerged}
        </Dropdown>
      )
    }

    return (
      <DateRangeInput
        ref={inputRef}
        disabledPopoverMessage={disabledPopoverMessage}
        disabledPopoverPlacement={disabledPopoverPlacement}
        errorDisplayType={errorDisplayType}
        errorMessage={errorMessage}
        errorPopoverPlacement={errorPopoverPlacement}
        hasLabelTooltip={hasLabelTooltip}
        hasValueTooltip={hasValueTooltip}
        isClearIconVisible={isClearIconVisible}
        isDisabled={isDisabled}
        isFullWidth={isFullWidth}
        isRequired={isRequired}
        isValid={isValid}
        label={label}
        locale={locale}
        name={name}
        prefixIcons={prefixIcons}
        renderLabel={renderLabel}
        selected={selectedProcessed}
        size={size}
        suffixIcons={suffixIcons}
        dropdownProps={{
          isVisible: open,
          placement,
          isGlobal,
          onVisibilityChange,
          menu: (
            <DateRangePickerMenu
              ref={menuRef}
              fromDate={fromDate}
              hasInputModes={hasInputModes}
              hasTimezoneLabel={hasTimezoneLabel}
              inputMode={inputModeProcessed}
              isVisible={open}
              labels={labels}
              language={language}
              locale={locale}
              parsingErrorMessages={parsingErrorMessages}
              presetOptions={presetOptions}
              presetsOrder={presetsOrder}
              selected={selectedProcessed}
              timezone={timezone}
              timezonePopoverContent={timezonePopoverContent}
              toDate={toDate}
              onClose={handleClose}
              onSelect={handleSelect}
            />
          ),
        }}
        onClear={handleClear}
        onMouseDown={handleOpen}
        onPointerEnter={onPointerEnter}
        onPointerLeave={onPointerLeave}
      />
    )
  }
)
