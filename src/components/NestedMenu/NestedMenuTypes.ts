import { ReactNode } from "react"

import type { MenuProps } from "components"
import { NestedMenuItem, NestedMenuItemParams } from "types"

type NestedMenuItemEnriched = NestedMenuItem & {
  customMenuId?: string
}

export type NestedMenuProps = Omit<NestedMenuItemParams, "items" | "urlKey"> & {
  onSelect?: (value: NestedMenuItem | null) => void
  onKeyDown?: (event: KeyboardEvent) => void
  items: Array<NestedMenuItemEnriched>
  renderMenu?: (activeCustomMenuId?: string) => ReactNode | boolean
  isItemSelected: (item: NestedMenuItem) => boolean
} & Pick<
    MenuProps,
    | "size"
    | "renderItem"
    | "isNavigationEnabled"
    | "isSelectByClickEnabled"
    | "isSelectByEnterEnabled"
  >

export type RefType = {
  onBack: () => string
  onReset: () => void
}
