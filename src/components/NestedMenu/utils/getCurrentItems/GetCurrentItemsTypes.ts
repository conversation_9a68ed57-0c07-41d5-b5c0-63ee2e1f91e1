import type { MenuItem } from "components/Menu/MenuTypes"
import type { NestedMenuProps } from "components/NestedMenu/NestedMenuTypes"
import { NestedMenuItem } from "types"

type GetCurrentItemsParams = Pick<
  NestedMenuProps,
  "items" | "childrenKey" | "translateFunc" | "titleKey" | "isItemSelected"
> & {
  activeItemKey: string
}

type GetCurrentItemsReturn = {
  currentItems: Array<MenuItem>
  selectedIndex: number | null
  activeItemKey: string
  activeItem: NestedMenuItem | null
}

export type GetCurrentItems = (
  params: GetCurrentItemsParams
) => GetCurrentItemsReturn
