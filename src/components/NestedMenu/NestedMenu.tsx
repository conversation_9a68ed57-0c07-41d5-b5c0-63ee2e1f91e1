import React, { forwardRef } from "react"

import { Menu } from "components"

import { useNestedMenu } from "./hooks"

import { StyledNestedMenu } from "./StyledNestedMenu"

import { NestedMenuProps, RefType } from "./NestedMenuTypes"

export const NestedMenu = forwardRef<RefType, NestedMenuProps>(
  (
    {
      // General nested props
      items,
      titleKey,
      childrenKey,
      translateFunc,
      // NestedMenu props
      size,
      renderMenu = () => false,
      renderItem,
      onSelect,
      isNavigationEnabled,
      isSelectByClickEnabled,
      isSelectByEnterEnabled,
      isItemSelected,
    },
    ref
  ) => {
    const { activeCustomMenuId, currentItems, handleSelect, selectedIndex } =
      useNestedMenu({
        items,
        titleKey,
        childrenKey,
        translateFunc,
        onSelect,
        ref,
        isItemSelected,
      })

    return (
      <StyledNestedMenu>
        {renderMenu(activeCustomMenuId) || (
          <Menu
            isNavigationEnabled={isNavigationEnabled}
            isSelectByClickEnabled={isSelectByClickEnabled}
            isSelectByEnterEnabled={isSelectByEnterEnabled}
            items={currentItems}
            renderItem={renderItem}
            selectedIndex={selectedIndex}
            size={size}
            onSelect={handleSelect}
          />
        )}
      </StyledNestedMenu>
    )
  }
)
