import { checkIsFunction } from "utils"

export const buildEventHandlers = ({
  value,
  isDisabled,
  hasKeyEventListener,
  hasClearIcon: hasClearIconProp,
  onFocus,
  onBlur,
  onClear,
  startIncrement,
  stopIncrement,
  startDecrement,
  stopDecrement,
  handleStepUp,
  handleStepDown,
  handleClear: handleClearProp,
  normalizeDisplayedValue,
}) => {
  const handleFocus = (event) => {
    if (isDisabled) {
      return
    }

    event.target.select()
    if (checkIsFunction(onFocus)) {
      onFocus(value)
    }
  }

  const handleBlur = () => {
    if (isDisabled) {
      return
    }

    if (checkIsFunction(onBlur)) {
      onBlur(value)
    }
    normalizeDisplayedValue()
  }

  const handleKeyDown = (event) => {
    if (event.code === "ArrowUp" || event.code === "ArrowDown") {
      event.preventDefault()
    }
    const isBlocked = event.repeat || isDisabled || !hasKeyEventListener

    if (isBlocked) {
      return
    }
    if (event.code === "ArrowUp") {
      handleStepUp()
      startIncrement()
    }
    if (event.code === "ArrowDown") {
      handleStepDown()
      startDecrement()
    }
  }

  const handleKeyUp = (event) => {
    if (event.code === "ArrowUp" || event.code === "ArrowDown") {
      event.preventDefault()
    }
    const isBlocked = event.repeat || isDisabled || !hasKeyEventListener

    if (isBlocked) {
      return
    }
    if (event.code === "ArrowUp") {
      stopIncrement()
    }
    if (event.code === "ArrowDown") {
      stopDecrement()
    }
  }

  const handleClear = (() => {
    const hasClearIcon: boolean =
      !isDisabled && (hasClearIconProp || checkIsFunction(onClear))

    if (!hasClearIcon) {
      return null
    }

    return (event) => {
      handleClearProp()

      if (checkIsFunction(onClear)) {
        onClear(event)
      }
    }
  })()

  return {
    handleBlur,
    handleFocus,
    handleKeyDown,
    handleKeyUp,
    handleClear,
  }
}
