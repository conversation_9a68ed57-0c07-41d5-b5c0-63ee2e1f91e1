import { ChangeEvent<PERSON><PERSON><PERSON>, useCallback, useEffect, useState } from "react"

import {
  format,
  getInitialValue,
  normalize,
} from "components/NumericInput/utils"
import { checkIsFunction } from "utils"

import { UseValue } from "./useValueTypes"

export const useValue: UseValue = ({
  value: valueProp,
  defaultValue,
  isStringMode,
  onChange,
  onClear,
  isDecimalAllowed,
  isNegativeAllowed,
  isZeroAllowed,
  minimumFractionDigits,
  maximumFractionDigits,
  locale,
}) => {
  const [value, setValue] = useState<number | string>(() =>
    getInitialValue({
      value: valueProp,
      defaultValue,
      isStringMode,
      isDecimalAllowed,
      isNegativeAllowed,
      isZeroAllowed,
      minimumFractionDigits,
      maximumFractionDigits,
      locale,
    })
  )

  const [displayedValue, setDisplayedValue] = useState<string>(
    () =>
      getInitialValue({
        value: valueProp,
        defaultValue,
        isStringMode: true,
        isDecimalAllowed,
        isNegativeAllowed,
        isZeroAllowed,
        minimumFractionDigits,
        maximumFractionDigits,
        locale,
      }) as string
  )

  const updateDisplayedValue = useCallback(
    (valueNew: string | number): void => {
      const valueFormatted = format({
        value: normalize(valueNew),
        isDecimalAllowed,
        isNegativeAllowed,
        isZeroAllowed,
        minimumFractionDigits,
        maximumFractionDigits,
        locale: "en-US",
      })

      const valueFormattedAsNumber = Number(valueFormatted)

      const isInvalid =
        valueFormatted === "" || Number.isNaN(valueFormattedAsNumber)

      if (isInvalid) {
        setDisplayedValue("")

        return
      }

      const displayedValueAsNumber: number | null =
        displayedValue === "" ? null : Number(displayedValue)

      const isSame: boolean =
        valueFormattedAsNumber === displayedValueAsNumber &&
        !valueFormatted.endsWith(".")

      if (isSame) {
        return
      }

      const valueFormattedWithLocale = format({
        value: normalize(valueNew),
        isDecimalAllowed,
        isNegativeAllowed,
        isZeroAllowed,
        minimumFractionDigits,
        maximumFractionDigits,
        locale,
      })

      setDisplayedValue(valueFormattedWithLocale)
    },
    [
      isDecimalAllowed,
      isNegativeAllowed,
      isZeroAllowed,
      minimumFractionDigits,
      maximumFractionDigits,
      displayedValue,
      locale,
    ]
  )

  // Update value when valueProp changes
  useEffect(() => {
    if (valueProp === undefined) {
      return
    }
    setValue(valueProp)
  }, [valueProp])

  // Update displayedValue when value changes
  useEffect(() => {
    updateDisplayedValue(value)
  }, [
    value,
    isDecimalAllowed,
    isNegativeAllowed,
    isZeroAllowed,
    minimumFractionDigits,
    maximumFractionDigits,
  ])

  const updateValuesInNumberMode = (newValue: number): void => {
    if (isStringMode === true) {
      return
    }

    if (valueProp === undefined) {
      setValue(newValue)
    }
    if (checkIsFunction(onChange)) {
      onChange(newValue)
    }
  }

  const updateValuesInStringMode = (newValue: string): void => {
    if (!isStringMode) {
      return
    }

    if (valueProp === undefined) {
      setValue(newValue)
    }
    if (checkIsFunction(onChange)) {
      onChange(newValue)
    }
  }

  const handleChange: ChangeEventHandler<HTMLInputElement> = (event): void => {
    const valueFormatted = format({
      value: event.target.value,
      isDecimalAllowed,
      isNegativeAllowed,
      isZeroAllowed,
      maximumFractionDigits,
      locale: "en-US",
    })
    const valueFormattedWithLocale = format({
      value: event.target.value,
      isDecimalAllowed,
      isNegativeAllowed,
      isZeroAllowed,
      maximumFractionDigits,
      locale,
    })

    if (!valueFormatted) {
      updateValuesInNumberMode(null)
      updateValuesInStringMode("")
      setDisplayedValue("")

      return
    }

    const valueParsed = Number(valueFormatted)

    // Update only displayed value, omit value change in the following cases:
    // 1. Separator added at the end
    const hasSeparatorAtTheEnd: boolean = valueFormatted.endsWith(".")
    // 2. Only minus sign is entered
    const hasOnlyMinusSign: boolean = valueFormatted === "-"

    const valuePrev: number | string | null | undefined =
      valueProp === undefined ? value : valueProp

    const valuePrevAsNumber: number | null =
      valuePrev !== null && valuePrev !== undefined ? Number(valuePrev) : null

    const isSameNumber: boolean = valueParsed === valuePrevAsNumber
    const isSameString: boolean = valueFormattedWithLocale === displayedValue
    // 3. In string mode, both the value and its formatted version are the same
    // 4. In number mode, check if the value is the same, ignore the formatted value because it will be the same number
    // even for different formatted values, e.g. 1.0 and 1, 1.00 and 1, etc.
    const isSame: boolean = isStringMode
      ? isSameNumber && isSameString
      : isSameNumber

    const shouldUpdateOnlyDisplayedValue: boolean =
      hasSeparatorAtTheEnd || hasOnlyMinusSign || isSame

    if (shouldUpdateOnlyDisplayedValue) {
      setDisplayedValue(valueFormattedWithLocale)

      return
    }

    const valueNum = ["", "-"].includes(valueFormatted) ? null : valueParsed

    updateValuesInNumberMode(valueNum)
    updateValuesInStringMode(valueFormatted)
    setDisplayedValue(valueFormattedWithLocale)
  }

  const handleClear = (): void => {
    if (valueProp === undefined) {
      setValue(null)
      setDisplayedValue("")
    }
    if (checkIsFunction(onChange)) {
      updateValuesInNumberMode(null)
      updateValuesInStringMode(null)
    }
    if (checkIsFunction(onClear)) {
      onClear()
    }
  }

  const normalizeDisplayedValue = (): void => {
    const shouldClear: boolean =
      ["", "-", "."].includes(displayedValue) ||
      (!isZeroAllowed && ["0", "0."].includes(displayedValue))

    if (shouldClear) {
      setDisplayedValue("")

      return
    }

    const valueAsNumber = Number(valueProp !== undefined ? valueProp : value)

    if (Number.isNaN(valueAsNumber)) {
      setDisplayedValue("")

      return
    }

    const displayedValueNew = valueAsNumber.toLocaleString(locale, {
      minimumFractionDigits,
      maximumFractionDigits,
      useGrouping: false,
    })

    setDisplayedValue(displayedValueNew)
  }

  return {
    value,
    displayedValue,
    setValue,
    handleChange,
    handleClear,
    normalizeDisplayedValue,
    updateValuesInNumberMode,
    updateValuesInStringMode,
  }
}
