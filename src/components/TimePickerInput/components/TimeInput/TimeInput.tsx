import React, { forwardRef } from "react"

import { InputContainer } from "components"

import { useTimeInput } from "./hooks"

import { StyledTimeInput } from "./StyledTimeInput"

import { TimeInputProps } from "./TimeInputTypes"

export const TimeInput = forwardRef<HTMLDivElement, TimeInputProps>(
  (
    {
      value: valueProp,
      isPopoverVisible,
      isActive,
      isDisabled,
      isFullWidth,
      isRequired,
      isValid,
      label,
      renderLabel,
      size,
      prefixIcons,
      suffixIcons: suffixIconsProp,
      errorMessage,
      errorDisplayType,
      errorPopoverPlacement,
      disabledPopoverMessage,
      disabledPopoverPlacement,
      hasClearIcon,
      hasMinutes,
      hasSeconds,
      onBlur,
      onClear,
      onClick,
      onFocus,
      onMouseDown,
      onSelect,
      onPointerEnter,
      onPointerLeave,
      dropdownProps,
      hasLabelTooltip,
      hasValueTooltip,
    },
    ref
  ) => {
    const {
      handleChange,
      placeholderRemainder,
      value,
      suffixIcons,
      inputRef,
      isFilled,
    } = useTimeInput({
      value: valueProp,
      label,
      suffixIcons: suffixIconsProp,
      isPopoverVisible,
      isActive,
      isDisabled,
      hasClearIcon,
      hasMinutes,
      hasSeconds,
      onClear,
      onSelect,
    })

    return (
      <InputContainer
        ref={ref}
        disabledPopoverMessage={disabledPopoverMessage}
        disabledPopoverPlacement={disabledPopoverPlacement}
        dropdownProps={dropdownProps}
        errorDisplayType={errorDisplayType}
        errorMessage={errorMessage}
        errorPopoverPlacement={errorPopoverPlacement}
        hasLabelTooltip={hasLabelTooltip}
        hasValueTooltip={hasValueTooltip}
        isDisabled={isDisabled}
        isFilled={isFilled}
        isFullWidth={isFullWidth}
        isRequired={isRequired}
        isValid={isValid}
        label={label}
        prefixIcons={prefixIcons}
        renderLabel={renderLabel}
        size={size}
        suffixIcons={suffixIcons}
        valueTooltip={value}
        containerProps={{
          onMouseDown,
          onClick,
          onPointerEnter,
          onPointerLeave,
        }}
      >
        <StyledTimeInput data-disabled={isDisabled}>
          <div>
            <span>{value}</span>
            <span>{placeholderRemainder}</span>
          </div>

          <input
            ref={inputRef}
            disabled={isDisabled}
            value={value}
            onBlur={onBlur}
            onChange={handleChange}
            onFocus={onFocus}
          />
        </StyledTimeInput>
      </InputContainer>
    )
  }
)
