import React, { forwardRef } from "react"

import { Button, TimePicker, TimezoneLabel } from "components"

import { useTimePickerMenu } from "./hooks"

import { TimeInput } from "../TimeInput"
import { StyledTimePickerMenu } from "./StyledTimePickerMenu"

import { TimePickerMenuProps } from "./TimePickerMenuTypes"

export const TimePickerMenu = forwardRef<HTMLDivElement, TimePickerMenuProps>(
  (
    {
      defaultDate,
      labels,
      timezone,
      timezonePopoverContent,
      isVisible,
      hasSeconds,
      hasMinutes,
      onClose,
      onSelect,
    },
    ref
  ) => {
    const { date, onSelectDate, onSave } = useTimePickerMenu({
      defaultDate,
      onSelect,
      onClose,
    })

    return (
      <StyledTimePickerMenu ref={ref}>
        <div data-component-type="input">
          <TimeInput
            isFullWidth
            hasMinutes={hasMinutes}
            hasSeconds={hasSeconds}
            value={date}
            onSelect={onSelectDate}
          />
        </div>

        <TimezoneLabel
          label={labels.timezone}
          popoverContent={timezonePopoverContent}
          timezone={timezone}
        />

        <TimePicker
          hasMinutes={hasMinutes}
          hasSeconds={hasSeconds}
          isVisible={isVisible}
          selected={date}
          onSelect={onSelectDate}
        />

        <div data-component-type="buttons">
          <Button type="button" variant="secondary" onClick={onClose}>
            {labels.cancel}
          </Button>

          <Button disabled={!date} type="button" onClick={onSave}>
            {labels.save}
          </Button>
        </div>
      </StyledTimePickerMenu>
    )
  }
)
