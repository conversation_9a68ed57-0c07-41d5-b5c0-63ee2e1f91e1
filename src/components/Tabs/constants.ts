import { IconSizes } from "components/Icon/IconTypes"

import { Position, Size, Variant } from "./TabsTypes"

export const ICON_SIZES_MAPPER: Record<Size, IconSizes> = {
  small: "--icon-size-1",
  medium: "--icon-size-3",
  big: "--icon-size-6",
}

export const ICON_ONLY_SIZES_MAPPER: Record<Size, IconSizes> = {
  small: "--icon-size-4",
  medium: "--icon-size-6",
  big: "--icon-size-5",
}

export const POSITION_TO_BORDER_MAPPER: Record<Position, Position> = {
  left: "right",
  right: "left",
  top: "bottom",
  bottom: "top",
}

export const VARIANTS: Record<Variant, Variant> = {
  default: "default",
  outlined: "outlined",
}

export const POSITIONS: Record<Position, Position> = {
  left: "left",
  right: "right",
  top: "top",
  bottom: "bottom",
}

export const SIZES: Record<Size, number> = {
  small: 32,
  medium: 44,
  big: 64,
}
