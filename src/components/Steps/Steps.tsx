import React, { Fragment } from "react"

import { Box, Icon, Typography } from "components"
import { checkIsArray } from "utils"

import { StyledSteps } from "./StyledSteps"

import { StepsProps } from "./StepsTypes"

export const Steps = ({
  steps,
  activeStep: activeStepProp = 0,
  descriptionMinWidth,
}: StepsProps) => {
  if (!checkIsArray(steps)) {
    return null
  }

  let activeStep = activeStepProp

  if (activeStepProp < 0) {
    activeStep = 0
  }
  if (activeStepProp >= steps.length) {
    activeStep = steps.length - 1
  }

  return (
    <StyledSteps>
      {steps.map((title, index) => {
        const isActive = index === activeStep
        const isCompleted = index < activeStep
        const isHighlighted = isActive || isCompleted
        const order = index + 1

        return (
          // eslint-disable-next-line react/no-array-index-key
          <Fragment key={index}>
            {index > 0 ? (
              <div
                data-component-type="step-line"
                data-is-highlighted={isHighlighted}
              />
            ) : null}

            <Box
              align="center"
              gap="m"
              justify="center"
              minWidth={descriptionMinWidth}
              mSM={{
                flexDirection: "column",
              }}
              tb={{
                flexDirection: "row",
              }}
            >
              <div
                data-component-type="step-circle"
                data-is-highlighted={isHighlighted}
              >
                {isCompleted ? (
                  <Icon
                    color="--color-icon-active"
                    name="icnCheck"
                    size="--icon-size-1"
                  />
                ) : (
                  <Typography
                    variant="--font-body-text-7"
                    color={
                      isHighlighted
                        ? "--color-text-link"
                        : "--color-text-disable"
                    }
                  >
                    {order}
                  </Typography>
                )}
              </div>

              <Box
                justify="center"
                minWidth={descriptionMinWidth}
                mSM={{
                  width: "min-content",
                }}
                tb={{
                  width: "max-content",
                }}
              >
                <Typography
                  variant="--font-body-text-7"
                  color={
                    isHighlighted ? "--color-text-main" : "--color-text-disable"
                  }
                >
                  {title}
                </Typography>
              </Box>
            </Box>
          </Fragment>
        )
      })}
    </StyledSteps>
  )
}
