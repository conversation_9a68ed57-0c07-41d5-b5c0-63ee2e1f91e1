import styled from "styled-components"

export const StyledSteps = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--gap-l);
  padding: var(--padding-l) 0;

  > div[data-component-type="step-line"] {
    flex-grow: 1;
    height: 1px;
    background-color: var(--color-int-off-active);

    &[data-is-highlighted="true"] {
      background-color: var(--color-int-on-active);
    }
  }

  > div {
    p {
      text-align: center;
    }

    > div[data-component-type="step-circle"] {
      display: flex;
      align-items: center;
      justify-content: center;
      width: var(--icon-size-6);
      min-width: var(--icon-size-6);
      height: var(--icon-size-6);
      min-height: var(--icon-size-6);
      border: 1px solid var(--color-int-off-active);
      border-radius: var(--border-radius-circle);

      &[data-is-highlighted="true"] {
        border-color: var(--color-int-on-active);
      }
    }
  }
`
