import { CheckboxProps, GridProps } from "components"

export type Value = string | number

export type Item = {
  value: Value
  label: string
  disabled?: boolean
}

export type ValuesArray = Array<Value>
export type ValuesObject = Record<Value, boolean>
export type Values = Array<Value> | Record<Value, boolean>
export type Mode = "array" | "object"

type PickedProps = Pick<
  CheckboxProps,
  "labelPosition" | "labelVariant" | "isLabelClickable"
>

export type CheckboxGroupProps = Partial<PickedProps> & {
  label?: string
  items: Array<Item>
  gridItemTemplateProps?: GridProps
  mode?: Mode
  onChange?: (values: Values) => void
  selectedValues?: Values
  hasEllipsis?: boolean
  hasEllipsisPopover?: boolean
  isDisabled?: boolean
}
