import React, { cloneElement, forwardRef } from "react"

import { getNodeChildOnly } from "utils"

import { StyledMenu } from "./StyledMenu"

import { MenuProps } from "./MenuTypes"

export const Menu = forwardRef<HTMLDivElement, MenuProps>(
  (
    {
      menu,
      width,
      minWidth,
      maxWidth,
      height,
      minHeight,
      maxHeight,
      isContentWidthMatchedWithTrigger,
      className,
      isVisible,
      isVisibilityPassedToMenu,
      onClose,
    },
    ref
  ) => {
    const shouldCheckForOnlyChild: boolean =
      isVisibilityPassedToMenu &&
      typeof menu !== "string" &&
      !Array.isArray(menu)

    const onlyChild = shouldCheckForOnlyChild ? getNodeChildOnly(menu) : null

    const shouldPassVisibilityPropsToMenu: boolean =
      !!onlyChild && typeof onlyChild.type !== "string"

    return (
      <StyledMenu
        ref={ref}
        className={className}
        dimensions={{ width, minWidth, maxWidth, height, minHeight, maxHeight }}
        isContentWidthMatchedWithTrigger={isContentWidthMatchedWithTrigger}
      >
        {shouldPassVisibilityPropsToMenu
          ? cloneElement(onlyChild, { ...onlyChild?.props, isVisible, onClose })
          : menu}
      </StyledMenu>
    )
  }
)
