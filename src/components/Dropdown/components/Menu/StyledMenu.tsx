import styled, { css, FlattenSimpleInterpolation } from "styled-components"

import { convertToDimension } from "utils"

import { StyledMenuProps } from "./MenuTypes"

const buildDimensionsStyle = ({
  dimensions: { width, maxWidth, minWidth, height, maxHeight, minHeight },
  isContentWidthMatchedWithTrigger,
}: StyledMenuProps): FlattenSimpleInterpolation => {
  let widthStyle = convertToDimension(width) || ""
  let maxWidthStyle = convertToDimension(maxWidth) || ""
  const minWidthStyle = convertToDimension(minWidth) || ""

  if (isContentWidthMatchedWithTrigger) {
    widthStyle = "100%"
    maxWidthStyle = "100%"
  }

  const heightStyle = convertToDimension(height) || ""
  const minHeightStyle = convertToDimension(minHeight) || ""
  const maxHeightStyle = convertToDimension(maxHeight) || ""

  return css`
    width: ${widthStyle};
    min-width: ${minWidthStyle};
    max-width: ${maxWidthStyle};
    height: ${heightStyle};
    min-height: ${minHeightStyle};
    max-height: ${maxHeightStyle};
  `
}

export const StyledMenu = styled.div<StyledMenuProps>`
  box-shadow: var(--box-shadow);
  background-color: var(--color-main-background);
  border-radius: var(--border-radius);
  overflow: auto;
  ${buildDimensionsStyle};
`
