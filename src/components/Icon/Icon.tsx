import React, { forwardRef } from "react"

import * as icons from "assets/icons"

import { IconWrapper, SvgIconStyled } from "./StyledIcon"

import { IconProps, IconRef } from "./IconTypes"

export const Icon = forwardRef<IconRef, IconProps>(
  (
    {
      name = null,
      size = "--icon-size-6",
      color = "--color-icon-clickable",
      hoverColor = "--color-icon-active",
      activeColor = "--color-icon-active",
      disableCursor = "",
      stroke = null,
      className = null,
      onClick = null,
      onPointerEnter = null,
      onPointerLeave = null,
      onMouseDown = null,
      onKeyDown = null,
      isColored = false,
      isHovered = false,
      isActive = false,
      isDisabled = false,
    },
    ref
  ) => {
    const SvgIcon = icons?.[name]
    const colorProps = !isColored ? { color, stroke } : {}

    return !SvgIcon ? null : (
      <IconWrapper
        ref={ref}
        activeColor={activeColor}
        aria-label={name}
        className={className}
        disableCursor={disableCursor}
        hoverColor={hoverColor}
        isActive={isActive}
        isDisabled={isDisabled}
        isHovered={isHovered}
        role="img"
        onClick={onClick}
        onKeyDown={onKeyDown}
        onMouseDown={onMouseDown}
        onPointerEnter={onPointerEnter}
        onPointerLeave={onPointerLeave}
      >
        <SvgIconStyled as={SvgIcon} name={name} size={size} {...colorProps} />
      </IconWrapper>
    )
  }
)
