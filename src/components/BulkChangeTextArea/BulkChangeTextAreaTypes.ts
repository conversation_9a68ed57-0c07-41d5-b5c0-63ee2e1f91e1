import type { InputWithDropdownContainerProps, TextAreaProps } from "components"
import type { BulkChangeCommonParams } from "types/BulkChangeCommonParams"

export type BulkChangeTextAreaProps = BulkChangeCommonParams &
  TextAreaProps &
  Pick<
    InputWithDropdownContainerProps,
    | "selectedModeValue"
    | "onSelectMode"
    | "gridContainerProps"
    | "modesGridItemProps"
    | "inputGridItemProps"
  >
