import { GetBorders } from "./GetBordersTypes"

const standardBorder = "var(--border-main)"

export const getBorders: GetBorders = (hasBorder) => {
  const hasAllBorders = typeof hasBorder === "boolean" && hasBorder
  const hasSideBorders = typeof hasBorder === "object" && hasBorder

  const border = hasAllBorders ? standardBorder : ""

  const borders = hasSideBorders
    ? Object.keys(hasBorder).reduce((acc, key) => {
        const keyProcessed = `border${key.charAt(0).toUpperCase()}${key.slice(
          1
        )}`

        let value = ""

        if (hasBorder[key] === true) {
          value = standardBorder
        }
        if (hasBorder[key] === false) {
          value = "none"
        }

        return {
          ...acc,
          [keyProcessed]: value,
        }
      }, {})
    : {}

  return { border, ...borders }
}
