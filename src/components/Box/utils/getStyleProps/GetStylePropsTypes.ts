import { CSSProperties } from "react"

import { BoxProps } from "components/Box/BoxTypes"

type GetStylePropsParams = Pick<
  BoxProps,
  | "padding"
  | "paddingTop"
  | "paddingRight"
  | "paddingBottom"
  | "paddingLeft"
  | "margin"
  | "marginTop"
  | "marginRight"
  | "marginBottom"
  | "marginLeft"
  | "gap"
  | "rowGap"
  | "columnGap"
  | "hasBorder"
  | "borderRadius"
  | "borderTopLeftRadius"
  | "borderTopRightRadius"
  | "borderBottomLeftRadius"
  | "borderBottomRightRadius"
  | "borderColor"
  | "borderTopColor"
  | "borderRightColor"
  | "borderBottomColor"
  | "borderLeftColor"
  | "backgroundColor"
  | "align"
  | "justify"
  | "opacity"
  | "boxShadow"
  | "isBlurred"
  | "blurRadius"
>

type GetStylePropsReturn = Pick<
  CSSProperties,
  | "padding"
  | "paddingTop"
  | "paddingRight"
  | "paddingBottom"
  | "paddingLeft"
  | "margin"
  | "marginTop"
  | "marginRight"
  | "marginBottom"
  | "marginLeft"
  | "gap"
  | "rowGap"
  | "columnGap"
  | "border"
  | "borderTop"
  | "borderRight"
  | "borderBottom"
  | "borderLeft"
  | "borderRadius"
  | "borderTopLeftRadius"
  | "borderTopRightRadius"
  | "borderBottomLeftRadius"
  | "borderBottomRightRadius"
  | "borderColor"
  | "borderTopColor"
  | "borderRightColor"
  | "borderBottomColor"
  | "borderLeftColor"
  | "backgroundColor"
  | "opacity"
  | "boxShadow"
  | "filter"
>

export type GetStyleProps = (params: GetStylePropsParams) => GetStylePropsReturn
