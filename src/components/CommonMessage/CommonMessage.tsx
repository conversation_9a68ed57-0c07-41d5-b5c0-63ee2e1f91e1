import React from "react"

import { Box, Icon, Typography } from "components"

import type { CommonMessageProps } from "./CommonMessageTypes"

export const CommonMessage = ({
  icon,
  iconColor = "--color-icon-static",
  title,
  description,
  action,
  boxProps,
}: CommonMessageProps) => {
  return (
    <Box
      align="center"
      flexDirection="column"
      gap="l"
      justify="center"
      padding="l"
      {...boxProps}
    >
      <Icon color={iconColor} name={icon} size="--icon-size-10" />

      {title ? (
        <Typography variant="--font-headline-2">{title}</Typography>
      ) : null}

      {description ? (
        <Typography color="--color-text-second" variant="--font-body-text-7">
          {description}
        </Typography>
      ) : null}

      {action}
    </Box>
  )
}
