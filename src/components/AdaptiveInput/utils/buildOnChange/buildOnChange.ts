import { checkIsFunction } from "utils"

import { BuildOnChangeParams, BuildOnChangeReturn } from "./BuildOnChangeTypes"

export const buildOnChange = <Type>({
  fieldOnChange,
  propsOnChange,
}: BuildOnChangeParams<Type>): BuildOnChangeReturn<Type> => {
  return (value: Type): void => {
    if (checkIsFunction(fieldOnChange)) {
      fieldOnChange(value)
    }

    if (checkIsFunction(propsOnChange)) {
      propsOnChange(value)
    }
  }
}
