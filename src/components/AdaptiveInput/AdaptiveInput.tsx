import React, { forwardRef } from "react"

import { InputAddon, InputGroup } from "components"

import { InputWithContainer, InputWithoutContainer } from "./components"

import { AdaptiveInputProps } from "./AdaptiveInputTypes"

export const AdaptiveInput = forwardRef<
  HTMLInputElement | HTMLTextAreaElement,
  AdaptiveInputProps
>((props, ref) => {
  const { type } = props

  switch (type) {
    case "text":
    case "textarea":
    case "numeric":
    case "select":
    case "selectWithSearch":
    case "tag":
    case "email":
    case "combinator":
    case "formatted":
    case "date":
    case "dateTime":
    case "dateRange":
    case "timepicker":
    case "phone": {
      const { addonBefore, addonAfter, ...rest } = props

      const hasAddons: boolean = !!addonBefore || !!addonAfter

      if (hasAddons) {
        return (
          <InputGroup isFullWidth size={rest.inputProps?.size}>
            {addonBefore ? <InputAddon>{addonBefore}</InputAddon> : null}
            <InputWithContainer ref={ref} {...rest} />
            {addonAfter ? <InputAddon>{addonAfter}</InputAddon> : null}
          </InputGroup>
        )
      }

      return <InputWithContainer ref={ref} {...rest} />
    }
    default: {
      return <InputWithoutContainer ref={ref} {...props} />
    }
  }
})
