import React from "react"

import { Box } from "components"
import { checkIsArray } from "utils"

import { DescriptionRow } from "./components"
import { useGridColumnTemplates } from "./hooks"

import { StyledContainer } from "./StyledDescriptions"

import { DescriptionsProps } from "./DescriptionsTypes"

export const Descriptions = ({
  boxContainerProps,
  gridContainerProps,
  gridRowTemplate,
  gridColumnTemplates: gridColumnTemplatesProp,
  titles,
  items,
}: DescriptionsProps) => {
  const gridColumnTemplates = useGridColumnTemplates({
    items,
    gridColumnTemplates: gridColumnTemplatesProp,
  })

  if (!checkIsArray(items)) {
    return null
  }

  return (
    <Box {...boxContainerProps} display="block">
      <StyledContainer
        container
        flexDirection="column"
        flexWrap="nowrap"
        gap="m"
        {...gridContainerProps}
      >
        {titles ? (
          <DescriptionRow
            isHeader
            columns={titles}
            gridColumnTemplates={gridColumnTemplates}
            gridRowTemplate={gridRowTemplate}
          />
        ) : null}

        {items.map(({ key, label, value }) => {
          return (
            <DescriptionRow
              key={key}
              columns={value}
              firstColumn={label}
              gridColumnTemplates={gridColumnTemplates}
              gridRowTemplate={gridRowTemplate}
            />
          )
        })}
      </StyledContainer>
    </Box>
  )
}
