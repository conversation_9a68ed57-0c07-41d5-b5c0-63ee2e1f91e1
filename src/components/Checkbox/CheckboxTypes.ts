import { HTMLAttributes } from "react"

import { ClickableLabelBoxProps } from "components"
import { IconNames } from "components/Icon/IconTypes"
import { SizesTypes } from "types"

export type CheckboxType = "default" | "collapse" | "expand" | "selectAll"

export type CheckboxProps = Omit<
  HTMLAttributes<HTMLInputElement>,
  "onChange"
> & {
  defaultChecked?: boolean
  checked?: boolean
  onChange?: (isChecked: boolean) => void
  disabled?: boolean
  className?: string
  type?: CheckboxType
  size?: Exclude<SizesTypes, "s">
  color?: string
  isFilled?: boolean
  label?: ClickableLabelBoxProps["label"]
  labelPosition?: ClickableLabelBoxProps["position"]
  labelVariant?: ClickableLabelBoxProps["variant"]
  isLabelClickable?: boolean
  hasEllipsis?: boolean
  hasEllipsisPopover?: boolean
}

export type IconsList = {
  [key: string]: IconNames
}
