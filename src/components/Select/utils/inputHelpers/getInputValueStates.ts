import { checkIsArray } from "utils"

export const getInputValueStates = ({
  isDropdownVisible,
  selected,
  searchValue,
  isInitialSearchVisisble,
}) => {
  const hasValue =
    (selected?.value !== null && selected?.value !== undefined) ||
    checkIsArray(selected)

  const hasValueAndPopoverIsNotVisible = hasValue && !isDropdownVisible
  const hasValueAndPopoverIsVisible = hasValue && isDropdownVisible
  const hasNoValueAndPopoverIsVisible = !hasValue && isDropdownVisible

  let displayedValue = ""
  let placeholder = ""

  if (hasValueAndPopoverIsNotVisible) {
    displayedValue = selected.label
  } else if (hasValueAndPopoverIsVisible) {
    displayedValue = searchValue
    placeholder = selected.label
  } else if (hasNoValueAndPopoverIsVisible) {
    displayedValue = searchValue
  } else if (isInitialSearchVisisble) {
    displayedValue = searchValue
  }

  return { hasValue, displayedValue, placeholder }
}
