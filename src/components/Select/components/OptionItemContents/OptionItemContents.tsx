import React from "react"

import { Icon, Tag } from "components"

import { StyledOptionItemContents } from "./StyledOptionItemContents"

export const OptionItemContents = ({
  renderOption,
  option,
  index,
  isMultiSelect,
  hasCheckIcon,
  isSelected,
}) => {
  const isCheckIconVisible = isSelected && hasCheckIcon && isMultiSelect

  if (renderOption) {
    return renderOption({ option, index, isSelected })
  }

  const { label, tagColor } = option

  return (
    <StyledOptionItemContents>
      {tagColor ? (
        <Tag color={tagColor} isDefault={false} name={label} size="xs" />
      ) : null}

      <div data-component-type="option-content">
        <div title={label}>{label}</div>
      </div>

      {isCheckIconVisible ? (
        <Icon
          color="--color-icon-active"
          name="icnCheck"
          size="--icon-size-2"
        />
      ) : null}
    </StyledOptionItemContents>
  )
}
