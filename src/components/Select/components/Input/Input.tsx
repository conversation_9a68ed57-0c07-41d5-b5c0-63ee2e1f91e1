import React, { forwardRef, PointerEvent, useMemo, useState } from "react"

import {
  MultiSelectInput,
  SingleSelectInput,
} from "components/Select/components"
import { useInput } from "components/Select/hooks"
import { getInputValueStates } from "components/Select/utils"
import { checkIsFunction } from "utils"

import { InputProps } from "./InputTypes"

export const Input = forwardRef<HTMLDivElement, InputProps>(
  (
    {
      name,
      label,
      renderLabel,
      selected,
      displayedValue: displayedValueProp,
      disabledValues,
      onClick,
      onClear,
      onSearch,
      searchValue,
      isDropdownVisible,
      onFocus,
      onBlur,
      onMouseDown,
      onMouseUp,
      onTouchStart,
      onTouchEnd,
      onPointerEnter,
      onPointerLeave,
      onRemoveSelectedItem,
      initializeConsideredIndex,
      isFullWidth,
      isMultiLine,
      isRequired,
      hasLabelTooltip,
      hasValueTooltip,
      hasSearch,
      hasSearchIcon,
      prefixIcons,
      suffixIcons,
      hasChevronIcon,
      hasClearIcon,
      isValid,
      isDisabled,
      errorMessage,
      errorDisplayType,
      errorPopoverPlacement,
      disabledPopoverMessage,
      disabledPopoverPlacement,
      tagsMode,
      renderSelectedCount,
      size,
      isInitialSearchVisisble,
      dropdownProps,
    },
    ref
  ) => {
    const [isHovered, setIsHovered] = useState<boolean>(false)

    const isMultiSelect = Array.isArray(selected)

    const { hasValue, displayedValue, placeholder } = getInputValueStates({
      isDropdownVisible,
      selected,
      searchValue,
      isInitialSearchVisisble,
    })

    const suffixIconsProcessed = useInput({
      onClear,
      onSearch,
      searchValue,
      isDropdownVisible,
      initializeConsideredIndex,
      suffixIcons,
      hasChevronIcon,
      isHovered,
      hasValue,
      hasClearIcon,
      hasSearch,
      hasSearchIcon,
      isInitialSearchVisisble,
    })

    const isInitialSearchVisisbleAndSearchValueDisplayed =
      isInitialSearchVisisble && !!searchValue && !!displayedValue
    const isFilled =
      isDropdownVisible ||
      hasValue ||
      isInitialSearchVisisbleAndSearchValueDisplayed
    const isInputCaretHidden = searchValue === undefined

    const eventHandlers = useMemo(() => {
      if (isDisabled) {
        return {}
      }

      return {
        onMouseDown,
        onMouseUp,
        onTouchStart,
        onTouchEnd,
        onPointerEnter: (event: PointerEvent<HTMLDivElement>) => {
          if (checkIsFunction(onPointerEnter)) {
            onPointerEnter(event)
          }
          setIsHovered(true)
        },
        onPointerLeave: (event: PointerEvent<HTMLDivElement>) => {
          if (checkIsFunction(onPointerLeave)) {
            onPointerLeave(event)
          }
          setIsHovered(false)
        },
      }
    }, [
      isDisabled,
      onPointerEnter,
      onPointerLeave,
      onMouseDown,
      onMouseUp,
      onTouchStart,
      onTouchEnd,
    ])

    const inputContainerProps = {
      errorDisplayType,
      errorMessage,
      errorPopoverPlacement,
      disabledPopoverMessage,
      disabledPopoverPlacement,
      isDisabled,
      isFilled,
      isFullWidth,
      isMultiLine,
      isValid,
      isRequired,
      hasLabelTooltip,
      hasValueTooltip,
      label,
      renderLabel,
      prefixIcons,
      size,
      suffixIcons: suffixIconsProcessed,
      dropdownProps,
    }

    if (!isMultiSelect) {
      const displayedValueProcessed =
        displayedValueProp !== undefined ? displayedValueProp : displayedValue

      return (
        <SingleSelectInput
          ref={ref}
          eventHandlers={eventHandlers}
          hasSearch={hasSearch}
          inputContainerProps={inputContainerProps}
          isInputCaretHidden={isInputCaretHidden}
          name={name}
          placeholder={placeholder}
          value={displayedValueProcessed}
          onBlur={onBlur}
          onFocus={onFocus}
          onSearch={onSearch}
        />
      )
    }

    const isCountMode = tagsMode === "count"

    return (
      <MultiSelectInput
        ref={ref}
        disabledValues={disabledValues}
        eventHandlers={eventHandlers}
        hasSearch={hasSearch}
        inputContainerProps={inputContainerProps}
        isActive={isDropdownVisible}
        isCountMode={isCountMode}
        isInputCaretHidden={isInputCaretHidden}
        name={name}
        renderSelectedCount={renderSelectedCount}
        searchValue={searchValue}
        selected={selected}
        onBlur={onBlur}
        onClick={onClick}
        onFocus={onFocus}
        onRemoveSelectedItem={onRemoveSelectedItem}
        onSearch={onSearch}
      />
    )
  }
)
