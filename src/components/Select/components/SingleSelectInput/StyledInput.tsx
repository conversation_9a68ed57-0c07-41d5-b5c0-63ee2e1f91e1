import styled, { css, FlattenSimpleInterpolation } from "styled-components"

import { StyledInputProps } from "./SingleSelectInputTypes"

const buildCaretColorStyles = ({
  isInputCaretHidden,
}: StyledInputProps): FlattenSimpleInterpolation => {
  if (isInputCaretHidden) {
    return css`
      caret-color: transparent;
    `
  }
}

const buildColorStyles = ({
  isPlaceholderVisible,
}: StyledInputProps): FlattenSimpleInterpolation => {
  if (isPlaceholderVisible) {
    return css`
      && {
        color: var(--color-text-placeholders) !important;
      }
    `
  }
}

export const StyledInput = styled.input<StyledInputProps>`
  ${buildCaretColorStyles}
  ${buildColorStyles}
`
