import React, {
  forwardRef,
  ReactNode,
  useCallback,
  useImperativeHandle,
  useRef,
} from "react"

import { TagInput } from "components"
import { checkIsFunction } from "utils"

import { buildTagInputProps } from "./utils"

import { MultiSelectInputProps } from "./MultiSelectInputTypes"

export const MultiSelectInput = forwardRef<
  HTMLDivElement,
  MultiSelectInputProps
>(
  (
    {
      name,
      searchValue,
      selected,
      disabledValues,
      inputContainerProps,
      eventHandlers,
      isInputCaretHidden,
      isCountMode,
      isActive,
      hasSearch,
      renderSelectedCount,
      onClick,
      onSearch,
      onBlur,
      onFocus,
      onRemoveSelectedItem,
    },
    ref
  ) => {
    const inputRef = useRef<HTMLInputElement>()

    useImperativeHandle(ref, () => inputRef.current)

    const inputValue: string | null = hasSearch ? searchValue : null

    const renderCount = useCallback(
      (count: number): ReactNode => {
        return isActive ? null : renderSelectedCount(count)
      },
      [isActive, renderSelectedCount]
    )

    const handleInputValueChange = (value): void => {
      const shouldCallOnSearch = hasSearch && checkIsFunction(onSearch)

      if (shouldCallOnSearch) {
        onSearch(value)
      }
    }

    const handleBlur = useCallback(
      (event): void => {
        const isOptionFocused =
          event.relatedTarget !== null &&
          event.relatedTarget.getAttribute("data-is-select-option") === "true"

        if (isOptionFocused) {
          const inputElement =
            inputRef.current?.querySelector("input") || inputRef.current

          inputElement?.focus()
        }

        if (onBlur) {
          onBlur(event)
        }
      },
      [onBlur]
    )

    const handleRemove = useCallback(
      (tag): void => {
        const option = selected.find((item) => item.label === tag)

        onRemoveSelectedItem(option)
      },
      [selected, onRemoveSelectedItem]
    )

    const { value, colors } = buildTagInputProps(selected)

    return (
      <TagInput
        ref={inputRef}
        colors={colors}
        disabledValues={disabledValues}
        hasCountModePopover={false}
        inputValue={inputValue}
        isCountMode={isCountMode}
        isInputCaretHidden={isInputCaretHidden}
        name={name}
        renderCount={renderCount}
        value={value}
        onBlur={handleBlur}
        onClick={onClick}
        onFocus={onFocus}
        onInputValueChange={handleInputValueChange}
        onRemove={handleRemove}
        {...inputContainerProps}
        {...eventHandlers}
      />
    )
  }
)
