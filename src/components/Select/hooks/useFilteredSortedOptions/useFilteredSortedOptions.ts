import { useMemo, useRef } from "react"

import type { Option } from "components/Select/SelectTypes"

import type {
  UseFilteredSortedOptions,
  UseFilteredSortedOptionsParams,
} from "./useFilteredSortedOptionsTypes"

export const useFilteredSortedOptions: UseFilteredSortedOptions = ({
  open,
  hasSearch,
  searchValue,
  options,
  filterOptions,
  selectedValues,
}) => {
  const propsRef = useRef<UseFilteredSortedOptionsParams>()

  const filteredSortedOptionsPrevious = useRef<Array<Option>>()

  return useMemo(() => {
    const propsCurrent = {
      open,
      hasSearch,
      searchValue,
      options,
      filterOptions,
      selectedValues,
    }

    // Check if options are the same
    const isSameProps =
      JSON.stringify(propsRef.current) === JSON.stringify(propsCurrent)

    // If options are the same, return the previous result
    if (isSameProps) {
      return filteredSortedOptionsPrevious.current
    }

    // Update optionsRef
    propsRef.current = propsCurrent

    const hasSearchValue = hasSearch && searchValue
    let optionsCopy = options

    if (hasSearchValue) {
      optionsCopy = options.filter((option) => {
        return filterOptions({ option, searchValue })
      })
    }

    let result: Array<Option> = []

    if (!(selectedValues instanceof Set)) {
      result = [
        ...optionsCopy.filter((option) => !option.disabled),
        ...optionsCopy.filter((option) => option.disabled),
      ]
    } else {
      result = [
        ...optionsCopy.filter(
          (option) => !option.disabled && selectedValues.has(option.value)
        ),
        ...optionsCopy.filter(
          (option) => !option.disabled && !selectedValues.has(option.value)
        ),
        ...optionsCopy.filter((option) => option.disabled),
      ]
    }

    filteredSortedOptionsPrevious.current = result

    return result
  }, [options, searchValue, hasSearch, open])
}
