import styled, { css } from "styled-components"

import { StyledBadgeItemProps } from "./BadgeItemTypes"

export const StyledBadgeItem = styled.div<StyledBadgeItemProps>`
  box-sizing: border-box;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: ${({ color }) => `var(${color})`};
  color: var(--color-text-white);
  cursor: default;

  :not(:empty) {
    max-width: max-content;
  }

  ${({ size }) => {
    const mapper = {
      l: css`
        height: var(--badge-size-l);
        min-width: var(--badge-size-l);
        font: var(--font-body-text-9);
        gap: var(--gap-m);
        border-radius: var(--border-radius-rounded);

        :not(:empty) {
          padding-left: var(--padding-l);
          padding-right: var(--padding-l);
        }
      `,
      m: css`
        height: var(--badge-size-m);
        min-width: var(--badge-size-m);
        font: var(--font-body-text-11);
        border-radius: var(--border-radius);

        :not(:empty) {
          padding-left: var(--padding-s);
          padding-right: var(--padding-s);
        }
      `,
      s: css`
        height: var(--badge-size-s);
        min-width: var(--badge-size-s);
        font: var(--font-body-text-11);
        border-radius: var(--border-radius-rounded);

        :not(:empty) {
          padding-left: var(--padding-s);
          padding-right: var(--padding-s);
        }
      `,
      xs: css`
        height: var(--badge-size-xs);
        width: var(--badge-size-xs);
        border-radius: var(--border-radius-rounded);
      `,
    }

    return mapper[size]
  }}
`
