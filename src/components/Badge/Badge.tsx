import React from "react"

import { BadgeItem, SuperscriptBadgeWrapper } from "./components"

import { BadgeProps } from "./BadgeTypes"

export const Badge = ({
  children,
  content,
  color = "--color-badge-1",
  size = "l",
  suffixIcons,
  prefixIcons,
  isContentMinified = true,
  superscriptTransform = ["50%", "-50%"],
}: BadgeProps) => {
  const badgeCommonProps =
    size === "xs"
      ? {
          color,
          size,
        }
      : {
          color,
          size,
          content,
          title: content ? content.toString() : "",
          isContentMinified,
        }

  if (size === "l") {
    return (
      <BadgeItem
        {...badgeCommonProps}
        isContentMinified={false}
        isSuperscript={false}
        prefixIcons={prefixIcons}
        suffixIcons={suffixIcons}
      />
    )
  }

  if (size === "m") {
    return (
      <BadgeItem
        {...badgeCommonProps}
        isContentMinified={false}
        isSuperscript={false}
      />
    )
  }

  if (!children) {
    return <BadgeItem {...badgeCommonProps} />
  }

  return (
    <SuperscriptBadgeWrapper
      {...badgeCommonProps}
      superscriptTransform={superscriptTransform}
    >
      {children}
    </SuperscriptBadgeWrapper>
  )
}
