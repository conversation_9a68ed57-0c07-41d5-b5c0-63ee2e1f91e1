import React, { forwardRef } from "react"

import { SortableList } from "../SortableList"
import { StyledSortableListWrapper } from "./StyledSortableListWrapper"

import { SortableListWrapperProps } from "./SortableListWrapperTypes"

export const SortableListWrapper = forwardRef<
  HTMLDivElement,
  SortableListWrapperProps
>(({ isDragInProgress, items, onSortStart, onSortEnd }, ref) => {
  return (
    <StyledSortableListWrapper ref={ref}>
      <SortableList
        hideSortableGhost={false}
        isDragInProgress={isDragInProgress}
        items={items}
        lockAxis="y"
        onSortEnd={onSortEnd}
        onSortStart={onSortStart}
      />
    </StyledSortableListWrapper>
  )
})
