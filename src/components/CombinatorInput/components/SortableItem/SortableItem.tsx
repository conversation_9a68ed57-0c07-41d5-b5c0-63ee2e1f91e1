import React from "react"
import { SortableElement } from "react-sortable-hoc"

import { Box, Icon, Typography } from "components"

import { StyledSortableItem } from "./StyledSortableItem"

import { SortableItemProps } from "./SortableItemTypes"

export const SortableItem = SortableElement(
  ({ value, order, orderSymbolWidth }: SortableItemProps) => {
    return (
      <StyledSortableItem>
        <Box align="center" height="100%" width={orderSymbolWidth}>
          <Typography
            color="--color-text-placeholders"
            component="div"
            variant="--font-body-text-7"
          >
            {order}
          </Typography>
        </Box>
        <Typography component="div" variant="--font-body-text-7">
          {value}
        </Typography>
        <Icon name="icnDrag" size="--icon-size-1" />
      </StyledSortableItem>
    )
  }
)
