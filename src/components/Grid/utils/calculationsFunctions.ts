import {
  doesAlways,
  doesAlwaysAuto,
  doesAlwaysCustom<PERSON>idth,
  doesAlwaysFull,
  doesAppliesSize,
  doesAppliesSizeAuto,
  doesAppliesSizeCustomWidth,
  doesAppliesSizeFull,
  doesContainerGap,
  hasContainerGap,
  stylesGap,
} from "components/Grid/utils"
import { gapSizes, marginSizes, paddingSizes } from "globalStyles"

import { gridColumns } from "../constants"

import {
  Always,
  AppliesSize,
  ContainerGap,
  Size,
  StylesGapProps,
} from "../types"

export const calcWidth = ({ size }: Size) =>
  `calc(100% * ${size} / ${gridColumns})`

export const calcWidthToAlways = ({ item, always }: Always): any => {
  const customWidth = doesAlwaysCustomWidth({
    item,
    always,
  })
    ? always
    : null
  const secondResult = doesAlwaysAuto({
    item,
    always,
  })
    ? "auto"
    : customWidth

  const result = doesAlways({
    item,
    always,
  })
    ? calcWidth({ size: always as number })
    : secondResult

  return result
}

export const calcGrow = ({
  item,
  always,
  notAllowedSize,
  appliesSize,
}: AppliesSize) => {
  const secondResult = doesAppliesSizeFull({
    item,
    always,
    notAllowedSize,
    appliesSize,
  })
    ? "1"
    : null

  const result = doesAppliesSize({
    item,
    always,
    notAllowedSize,
    appliesSize,
  })
    ? "0"
    : secondResult

  return result
}

export const calcGrowAlways = ({ item, always }: Always) => {
  const secondResult = doesAlwaysFull({
    item,
    always,
  })
    ? "1"
    : null

  const result = doesAlways({
    item,
    always,
  })
    ? "0"
    : secondResult

  return result
}

export const calcBasis = ({
  item,
  always,
  notAllowedSize,
  appliesSize,
}: AppliesSize) => {
  const secondResult = doesAppliesSizeFull({
    item,
    always,
    notAllowedSize,
    appliesSize,
  })
    ? "0"
    : null

  const result = doesAppliesSize({
    item,
    always,
    notAllowedSize,
    appliesSize,
  })
    ? "auto"
    : secondResult

  return result
}

export const calcBasisAlways = ({ item, always }: Always) => {
  const secondResult = doesAlwaysFull({ item, always }) ? "0" : null

  const result = doesAlways({
    item,
    always,
  })
    ? "auto"
    : secondResult

  return result
}

export const calcMaxWidth = ({
  item,
  always,
  notAllowedSize,
  appliesSize,
}: AppliesSize) => {
  const secondResult = doesAppliesSizeFull({
    item,
    always,
    notAllowedSize,
    appliesSize,
  })
    ? "100%"
    : null

  const result = doesAppliesSizeAuto({
    item,
    always,
    notAllowedSize,
    appliesSize,
  })
    ? "none"
    : secondResult

  return result
}

export const calcMaxWidthAlways = ({ item, always }: Always) => {
  const secondResult = doesAlwaysFull({ item, always }) ? "100%" : null

  const result = doesAlwaysAuto({ item, always }) ? "none" : secondResult

  return result
}

export const calcWidthToSize = ({
  item,
  always,
  notAllowedSize,
  appliesSize,
}: AppliesSize) => {
  const customWidth = doesAppliesSizeCustomWidth({
    item,
    always,
    notAllowedSize,
    appliesSize,
  })
    ? appliesSize
    : null
  const secondResult = doesAppliesSizeAuto({
    item,
    always,
    notAllowedSize,
    appliesSize,
  })
    ? "auto"
    : customWidth

  const result = doesAppliesSize({
    item,
    always,
    notAllowedSize,
    appliesSize,
  })
    ? calcWidth({ size: appliesSize as number })
    : secondResult

  return result
}

export const calcRowGap = ({
  container,
  gap,
  columnGap,
  rowGap,
}: ContainerGap) => {
  const isGap = doesContainerGap({
    container,
    appliesGap: gap,
    notAllowedFirstGap: columnGap,
    notAllowedSecondGap: rowGap,
  })

  const isColumnGap = doesContainerGap({
    container,
    appliesGap: columnGap,
    notAllowedFirstGap: gap,
  })

  const secondResult = isColumnGap ? `${paddingSizes[columnGap]}` : null

  const result = isGap ? `${gapSizes[gap]}` : secondResult

  return result
}

export const calcContainerMargin = ({
  container,
  gap,
  columnGap,
  rowGap,
}: ContainerGap) => {
  const isGap = doesContainerGap({
    container,
    appliesGap: gap,
    notAllowedFirstGap: columnGap,
    notAllowedSecondGap: rowGap,
  })

  const isRowGap = doesContainerGap({
    container,
    appliesGap: rowGap,
    notAllowedFirstGap: gap,
  })

  const secondResult = isRowGap ? `0 calc(${marginSizes[rowGap]} / -2)` : null

  const result = isGap ? `0 calc(${marginSizes[gap]} / -2)` : secondResult

  return result
}

export const calcItemPadding = ({
  container,
  gap,
  columnGap,
  rowGap,
}: ContainerGap) => {
  const isGap = doesContainerGap({
    container,
    appliesGap: gap,
    notAllowedFirstGap: columnGap,
    notAllowedSecondGap: rowGap,
  })

  const isRowGap = doesContainerGap({
    container,
    appliesGap: rowGap,
    notAllowedFirstGap: gap,
  })

  const secondResult = isRowGap ? `0 calc(${paddingSizes[rowGap]} / 2)` : null

  const result = isGap ? `0 calc(${paddingSizes[gap]} / 2)` : secondResult

  return result
}

export const calcGap = ({
  container,
  gap,
  rowGap,
  columnGap,
  additionalGap,
  additionalRowGap,
  additionalColumnGap,
}: StylesGapProps) => {
  const hasAdditionalGapsAndDoesNotHaveMainGaps =
    !hasContainerGap({ container, gap, rowGap, columnGap }) &&
    hasContainerGap({
      container,
      gap: additionalGap,
      rowGap: additionalRowGap,
      columnGap: additionalColumnGap,
    })

  return hasAdditionalGapsAndDoesNotHaveMainGaps
    ? stylesGap({
        container,
        gap: additionalGap,
        rowGap: additionalRowGap,
        columnGap: additionalColumnGap,
      })
    : null
}
