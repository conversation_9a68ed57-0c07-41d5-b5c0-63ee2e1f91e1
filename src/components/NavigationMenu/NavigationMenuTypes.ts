import { HTMLAttributes, JSXElementConstructor } from "react"

import { TranslateFuncTypes } from "types"

export interface Item {
  title: string
  url?: string
  badge?: string | number
  icon?: JSXElementConstructor<any>
}

export interface NavigationMenuProps extends HTMLAttributes<HTMLUListElement> {
  items: any[]
  titleKey?: string
  urlKey?: string
  childrenKey?: string
  beta?: string[]
  hiddenWidth?: string
  translateFunc?: TranslateFuncTypes
  language: string
}

export type StyledNavigationProps = {
  hiddenWidth?: string
}

export type StyledEllipsisProps = {
  isVisible: boolean
}
