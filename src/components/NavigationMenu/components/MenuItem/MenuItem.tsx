import React from "react"

import { checkIsArray } from "utils"

import { Badge } from "../Badge"
import { SubMenu } from "../SubMenu"
import { StyledLink, StyledMenuItem } from "./MenuItemStyled"

import { MenuItemProps } from "./MenuItemTypes"

export const MenuItem = ({
  item,
  titleKey = "id",
  urlKey = "url",
  childrenKey = "items",
  beta,
  translateFunc,
}: MenuItemProps) => {
  const newChildren = checkIsArray(item?.[childrenKey])

  if (newChildren) {
    return (
      <SubMenu
        isTopLevel
        beta={beta}
        childrenKey={childrenKey}
        item={item}
        titleKey={titleKey}
        translateFunc={translateFunc}
        urlKey={urlKey}
      />
    )
  }

  const newTitle = item?.[titleKey]
  const newUrl = item?.[urlKey]

  return (
    <StyledMenuItem key={newTitle}>
      <StyledLink href={newUrl}>
        {translateFunc ? translateFunc(newTitle) : newTitle}
      </StyledLink>
      {item?.badge ? <Badge title={item?.badge} /> : null}
      {beta?.some((label) => label === newTitle) ? (
        <Badge
          backgroundColor="--color-badge-1"
          textColor="--color-alert-background-done"
          title="beta"
        />
      ) : null}
    </StyledMenuItem>
  )
}
