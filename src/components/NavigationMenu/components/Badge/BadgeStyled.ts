import styled from "styled-components"

import { StyledBadgeProps } from "./BadgeTypes"

export const BadgeStyled = styled.div<StyledBadgeProps>`
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: ${({ backgroundColor }) => `var(${backgroundColor})`};
  //rewrite 32px to variable
  border-radius: ${({ isBetaTitle }) =>
    isBetaTitle ? "var(--border-radius)" : "32px"};
  padding: 0 var(--padding-s);
  color: var(--color-badge-background);
  margin-left: var(--margin-s);
  height: ${({ isBetaTitle }) =>
    isBetaTitle ? "var(--badge-size-m)" : "var(--badge-size-s)"};

  &:hover {
    color: var(--color-badge-background);
  }
`
