import React from "react"

import { Box, Typography } from "components"
import { SubMenu } from "components/NavigationMenu/components"
import { checkIsArray } from "utils"

import { Badge } from "../Badge"
import { StyledLink } from "../MenuItem/MenuItemStyled"
import { StyledSubMenu, StyledSubMenuItem } from "../SubMenu/SubMenuStyled"

import { SubMenuProps } from "./SubMenuItemsTypes"

export const SubMenuItems = ({
  calculateMenuPosition,
  isTopLevel,
  leftOffset,
  rightOffset,
  itemWithChildren,
  titleKey,
  urlKey,
  childrenKey,
  beta,
  isEllipsis,
  translateFunc,
}: SubMenuProps) => {
  return (
    <StyledSubMenu
      ref={calculateMenuPosition}
      isTopLevel={isTopLevel}
      leftOffset={leftOffset}
      rightOffset={rightOffset}
    >
      {itemWithChildren.map((items) => {
        if (checkIsArray(items?.[childrenKey])) {
          return (
            <SubMenu
              key={items?.[titleKey]}
              beta={beta}
              childrenKey={childrenKey}
              item={items}
              titleKey={titleKey}
              translateFunc={translateFunc}
              urlKey={urlKey}
            />
          )
        }

        const isNeedRenderBeta =
          isTopLevel &&
          isEllipsis &&
          beta?.some((label) => label === items?.[titleKey])

        return (
          <StyledSubMenuItem key={items?.[titleKey]}>
            <Box align="center">
              <Typography variant="--font-body-text-9">
                <StyledLink href={items?.[urlKey]}>
                  {translateFunc
                    ? translateFunc(items?.[titleKey])
                    : items?.[titleKey]}
                </StyledLink>
              </Typography>
              {items?.badge ? <Badge title={items?.badge} /> : null}
              {isNeedRenderBeta ? (
                <Badge
                  backgroundColor="--color-badge-1"
                  textColor="--color-alert-background-done"
                  title="beta"
                />
              ) : null}
            </Box>
          </StyledSubMenuItem>
        )
      })}
    </StyledSubMenu>
  )
}
