import { StyledMenuProps } from "../components"

export const calculatePosition = ({
  isTopLevel,
  leftOffset,
  rightOffset,
}: StyledMenuProps) => {
  let offset = isTopLevel ? "0" : "calc(100% + 4px)"

  if (rightOffset) {
    offset = isTopLevel ? "auto" : `-${rightOffset - leftOffset + 4}px`
  }

  return {
    top: isTopLevel ? "100%" : "0",
    left: offset,
    right: rightOffset && isTopLevel ? "0" : "auto",
  }
}
