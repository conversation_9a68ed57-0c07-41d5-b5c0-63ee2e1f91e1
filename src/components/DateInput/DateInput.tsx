import React, { forwardRef } from "react"
import enGB from "date-fns/locale/en-GB"

import { InputContainer } from "components/index"

import { useDateInput } from "./hooks"

import { StyledDateInput } from "./StyledDateInput"

import { DateInputProps } from "./DateInputTypes"

export const DateInput = forwardRef<HTMLDivElement, DateInputProps>(
  (
    {
      name,
      locale = enGB,
      selected,
      fromDate,
      toDate,
      onSelect,
      isPopoverVisible,
      isActive,
      hasTimePicker,
      datePlaceholderLetters,
      hasMinutes,
      hasSeconds,
      // InputContainer props
      label,
      renderLabel,
      size,
      isDisabled,
      isValid,
      isFullWidth,
      isRequired,
      onClear,
      prefixIcons,
      suffixIcons: suffixIconsProp,
      errorMessage,
      errorDisplayType,
      errorPopoverPlacement,
      disabledPopoverMessage,
      disabledPopoverPlacement,
      onMouseDown,
      onClick,
      onBlur,
      onFocus,
      onPointerEnter,
      onPointerLeave,
      dropdownProps,
      hasLabelTooltip,
      hasValueTooltip,
      parsingErrorMessages,
      onParsingError,
    },
    ref
  ) => {
    const {
      inputRef,
      suffixIcons,
      value,
      isFilled,
      placeholderRemainder,
      handleChange,
      parsingError,
      handleFocus,
      handleBlur,
    } = useDateInput({
      label,
      locale,
      selected,
      onSelect,
      isPopoverVisible,
      isActive,
      suffixIcons: suffixIconsProp,
      onClear,
      fromDate,
      toDate,
      hasTimePicker,
      isDisabled,
      datePlaceholderLetters,
      onFocus,
      onBlur,
      parsingErrorMessages,
      hasMinutes,
      hasSeconds,
      onParsingError,
    })

    return (
      <InputContainer
        ref={ref}
        disabledPopoverMessage={disabledPopoverMessage}
        disabledPopoverPlacement={disabledPopoverPlacement}
        dropdownProps={dropdownProps}
        errorDisplayType={errorDisplayType}
        errorMessage={parsingError || errorMessage}
        errorPopoverPlacement={errorPopoverPlacement}
        hasLabelTooltip={hasLabelTooltip}
        hasValueTooltip={hasValueTooltip}
        isActive={isActive}
        isDisabled={isDisabled}
        isFilled={isFilled}
        isFullWidth={isFullWidth}
        isRequired={isRequired}
        isValid={isValid}
        label={label}
        prefixIcons={prefixIcons}
        renderLabel={renderLabel}
        size={size}
        suffixIcons={suffixIcons}
        valueTooltip={value}
        containerProps={{
          onMouseDown,
          onClick,
          onPointerEnter,
          onPointerLeave,
        }}
      >
        <StyledDateInput data-disabled={isDisabled}>
          <div>
            <span>{value}</span>
            <span>{placeholderRemainder}</span>
          </div>
          <input
            ref={inputRef}
            disabled={isDisabled}
            name={name}
            value={value}
            onBlur={handleBlur}
            onChange={handleChange}
            onFocus={handleFocus}
          />
        </StyledDateInput>
      </InputContainer>
    )
  }
)
