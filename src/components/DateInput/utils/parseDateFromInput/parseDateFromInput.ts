import { isAfter, isBefore, isValid, parse } from "date-fns"

import {
  PARSING_ERROR_MESSAGES_DEFAULT,
  PARSING_STATUS_KEYS,
} from "components/DateInput/constants"

import { ParseDateFromInput } from "./ParseDateFromInputTypes"

export const parseDateFromInput: ParseDateFromInput = ({
  value,
  handleError,
  fromDate,
  toDate,
  placeholder,
  formatString,
  parsingErrorMessages = PARSING_ERROR_MESSAGES_DEFAULT,
}) => {
  const isIncomplete = value.length < placeholder.length

  if (isIncomplete) {
    return {
      date: null,
      status: PARSING_STATUS_KEYS.incomplete,
    }
  }

  const parsed = parse(value, formatString, new Date())

  if (!isValid(parsed)) {
    handleError({
      date: parsed,
      message: parsingErrorMessages.invalid,
    })

    return {
      date: null,
      status: PARSING_STATUS_KEYS.invalid,
    }
  }

  const isDisabled = isBefore(parsed, fromDate) || isAfter(parsed, toDate)

  if (isDisabled) {
    handleError({
      date: parsed,
      message: parsingErrorMessages.disabled,
    })

    return {
      date: null,
      status: PARSING_STATUS_KEYS.disabled,
    }
  }

  return {
    date: parsed,
    status: PARSING_STATUS_KEYS.valid,
  }
}
