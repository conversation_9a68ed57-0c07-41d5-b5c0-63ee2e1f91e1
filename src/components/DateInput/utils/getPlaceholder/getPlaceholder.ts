import { getTimeFormat } from "utils"
import { DATE_INPUT_FORMATS } from "consts"
import { AllowedTimeFormat } from "types"

import { getDateTimeFormat } from "../getDateTimeFormat"

import { GetPlaceholder } from "./GetPlaceholderTypes"

export const getPlaceholder: GetPlaceholder = ({
  locale,
  hasTimePicker,
  hasSeconds,
  hasMinutes,
}) => {
  const datePlaceholder: string = locale.formatLong
    .date({ width: "short" })
    .toUpperCase()
    .replace(/Y+/g, DATE_INPUT_FORMATS.YEAR)

  if (hasTimePicker) {
    // To ensure that there will be no conflicts with the time format.
    // The separators are extracted from the time format, but hours, minutes, seconds and AM/PM are of two digits.
    const dateTimeFormat = getDateTimeFormat(locale)
    const TIME_FORMAT: AllowedTimeFormat = getTimeFormat({
      hasMinutes,
      hasSeconds,
    })
    const dateTimePlaceholder = dateTimeFormat
      .replace("{{date}}", datePlaceholder)
      .replace("{{time}}", TIME_FORMAT)

    return {
      datePlaceholder,
      timePlaceholder: TIME_FORMAT,
      dateTimePlaceholder,
    }
  }

  return {
    datePlaceholder,
    timePlaceholder: "",
    dateTimePlaceholder: datePlaceholder,
  }
}
