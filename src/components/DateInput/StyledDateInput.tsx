import styled from "styled-components"

export const StyledDateInput = styled.div`
  position: relative;
  height: 100%;
  width: 100%;
  z-index: 1000;

  > div {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    z-index: -10;
    font: var(--font-body-text-9);
    white-space: nowrap;
    overflow: hidden;

    > span:first-child {
      color: var(--color-text-main);
    }

    > span:last-child {
      color: var(--color-text-placeholders);
    }
  }

  > input {
    color: transparent !important;
    caret-color: var(--color-text-main);
    z-index: 1000;
    height: 100%;
    width: 100%;
  }

  &[data-disabled="true"] {
    color: var(--color-text-disable);

    > input {
      user-select: none;
    }

    > div > span {
      color: var(--color-text-disable);
      user-select: none;
    }
  }
`
