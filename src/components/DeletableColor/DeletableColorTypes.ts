import { HTMLAttributes } from "react"

import type { CheckboxProps } from "components/Checkbox"

export type DeletableColorProps = HTMLAttributes<HTMLElement> & {
  className?: string
  color?: string
  onDelete?: () => void
  tooltip?: string
  isHiddenAfterDelete?: boolean
  isDisabled?: boolean
  onCheckboxChange?: CheckboxProps["onChange"]
  checked?: boolean
  checkboxClassName?: string
  isDeletable?: boolean
}

export type UseDeletableColorProps = {
  visible?: boolean
  handleDelete?: () => void
}

export type UseDeletableColorType = (
  params: Pick<DeletableColorProps, "onDelete" | "isHiddenAfterDelete">
) => UseDeletableColorProps
