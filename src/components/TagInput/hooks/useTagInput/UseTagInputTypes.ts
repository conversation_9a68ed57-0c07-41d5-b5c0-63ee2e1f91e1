import { ChangeEvent, RefObject } from "react"

import type { TagInputProps } from "components"

type UseTagInputParams = Pick<
  TagInputProps,
  | "value"
  | "defaultValue"
  | "inputValue"
  | "pattern"
  | "maxCount"
  | "validate"
  | "separator"
  | "errorMessagesMapper"
  | "isDisabled"
  | "hasClearIcon"
  | "onAdd"
  | "onRemove"
  | "onChange"
  | "onInputValueChange"
  | "onBlur"
  | "onClear"
  | "onTagClick"
  | "isTagEditable"
  | "disabledValues"
>

type UseTagInputReturn = Pick<TagInputProps, "value" | "inputValue"> & {
  inputRef: RefObject<HTMLInputElement>
  handleInputChange: (event: ChangeEvent<HTMLInputElement>) => void
  handleAddTag: TagInputProps["onAdd"]
  buildHandleCloseTag: (tag: string) => () => void
  handleRemoveLast: () => void
  handleBlur: TagInputProps["onBlur"]
  handleClear: TagInputProps["onClear"]
  handleTagClick: TagInputProps["onTagClick"]
  errors: string
  hasErrors: boolean
  isFocused: boolean
  setIsFocused: (isFocused: boolean) => void
}

export type UseTagInput = (params: UseTagInputParams) => UseTagInputReturn
