import { HTMLAttributes, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>actN<PERSON> } from "react"

import { InputContainerProps } from "../InputContainer"

type TagEventHandler = (tag: string) => void

type ValueEventHandler = (value: Array<string>) => void

type PickedProps = Pick<
  InputContainerProps,
  | "label"
  | "renderLabel"
  | "size"
  | "prefixIcons"
  | "suffixIcons"
  | "errorMessage"
  | "errorDisplayType"
  | "errorPopoverPlacement"
  | "disabledPopoverMessage"
  | "disabledPopoverPlacement"
  | "isFullWidth"
  | "isValid"
  | "isDisabled"
  | "isRequired"
  | "hasLabelTooltip"
  | "hasValueTooltip"
  | "dropdownProps"
> &
  Pick<
    HTMLAttributes<HTMLDivElement>,
    | "onClick"
    | "onPointerEnter"
    | "onPointerLeave"
    | "onMouseDown"
    | "onKeyDown"
    | "onBlur"
    | "onFocus"
  >

export type TagInputProps = {
  name?: string
  defaultValue?: Array<string>
  disabledValues?: Array<string>
  value?: Array<string>
  colors?: Array<string>
  inputValue?: string
  validate?: (value: string) => boolean
  placeholder?: string
  pattern?: string
  maxCount?: number
  maxVisibleCount?: number
  separator?: string
  errorMessagesMapper?: {
    duplicate?: string
    pattern?: string
    validate?: string
    maxCount?: string
  }
  isInputCaretHidden?: boolean
  isCountMode?: boolean
  isOverflowCountMode?: boolean
  isMultiLine?: boolean
  isTagEditable?: boolean
  hasCountModePopover?: boolean
  hasClearIcon?: boolean
  renderCount?: (count: number) => string | ReactNode
  onClear?: MouseEventHandler<HTMLSpanElement>
  onAdd?: TagEventHandler
  onRemove?: TagEventHandler
  onTagClick?: (tag: string) => void
  onChange?: ValueEventHandler
  onInputValueChange?: (value: string) => void
} & Partial<PickedProps>
