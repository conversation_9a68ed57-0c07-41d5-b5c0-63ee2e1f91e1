import React, { ReactNode, useMemo } from "react"
import get from "lodash.get"

import { Ellipsis } from "components/Ellipsis"
import { BaseData } from "components/SimpleTable/SimpleTableTypes"
import { checkIsFunction } from "utils"

import { DefaultRenderLoadingCell } from "../DefaultRenderLoadingCell"
import { StyledBody } from "./StyledBody"

import { BodyProps } from "./BodyTypes"

export const Body = <DataType extends BaseData>({
  data: dataProp,
  columns,
  loadingRowsCount,
  isLoading,
}: BodyProps<DataType>) => {
  const data = useMemo(() => {
    return isLoading
      ? Array(loadingRowsCount)
          .fill(0)
          .map((_, index) => ({
            id: index,
          }))
      : dataProp
  }, [dataProp, isLoading, loadingRowsCount])

  return (
    <StyledBody>
      {data.map((item, rowIndex) => {
        return (
          <tr key={item.id}>
            {columns.map(
              ({
                key,
                dataIndex,
                renderCell,
                hasEllipsis,
                ellipsisPopoverWidth,
                renderLoadingCell: renderLoadingCellParam,
                hasPadding = true,
              }) => {
                if (isLoading) {
                  const renderLoadingCell =
                    renderLoadingCellParam || DefaultRenderLoadingCell

                  return (
                    <td key={key} data-has-padding={hasPadding}>
                      <div>
                        {renderLoadingCell({
                          dataIndex,
                          index: rowIndex,
                          key,
                        })}
                      </div>
                    </td>
                  )
                }

                const value = get(item, dataIndex)

                const cell: ReactNode = checkIsFunction(renderCell)
                  ? renderCell({
                      value,
                      item,
                      key,
                      dataIndex,
                      index: rowIndex,
                    })
                  : value

                if (hasEllipsis) {
                  return (
                    <td key={key} data-has-padding={hasPadding}>
                      <div data-has-ellipsis>
                        <Ellipsis
                          rows={3}
                          popoverProps={{
                            maxWidth: ellipsisPopoverWidth,
                            width: ellipsisPopoverWidth,
                          }}
                          typographyProps={{
                            variant: "--font-service-text-1",
                          }}
                        >
                          {cell}
                        </Ellipsis>
                      </div>
                    </td>
                  )
                }

                return (
                  <td key={key} data-has-padding={hasPadding}>
                    <div data-has-ellipsis={false}>{cell}</div>
                  </td>
                )
              }
            )}
          </tr>
        )
      })}
    </StyledBody>
  )
}
