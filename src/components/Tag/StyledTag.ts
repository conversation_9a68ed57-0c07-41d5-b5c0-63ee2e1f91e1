import styled from "styled-components"

import { buildHoverStyle, buildTagColorsStyle, buildTagSize } from "./utils"

import { buildDisabledStyle } from "./utils/styleBuilders"

import { StyledTagProps } from "./TagTypes"

export const StyledTag = styled.div<StyledTagProps>`
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: ${({ margin }) => (margin ? `var(--margin-${margin})` : null)};
  max-width: 100%;
  overflow: hidden;

  > div,
  > span {
    z-index: 1;

    svg {
      color: inherit;
    }
  }

  ::after {
    content: "";
    box-sizing: border-box;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: var(--border-radius);
  }

  ${buildDisabledStyle}
  ${buildTagColorsStyle}
  ${buildTagSize}
  ${buildHoverStyle}
`
