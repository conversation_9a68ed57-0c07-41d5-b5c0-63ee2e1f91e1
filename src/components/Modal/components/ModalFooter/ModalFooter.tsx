import React from "react"

import { But<PERSON> } from "components/Button"

import { StyledButtonContainer, StyledModalFooter } from "./StyledModalFooter"

import { ModalFooterProps } from "./ModalFooterTypes"

export const ModalFooter = ({
  footer,
  isColumnButtonsOnMobile,
  cancelButtonText,
  okButtonText,
  okButtonProps,
  handleCancel,
  handleOk,
  isDisabledFooter,
  className,
  isLoading,
  cancelButtonProps,
}: ModalFooterProps) => {
  const isButtonTextDefined = cancelButtonText || okButtonText
  const isFooterHidden = (!footer || isDisabledFooter) && !isButtonTextDefined

  if (isFooterHidden) {
    return null
  }

  return (
    <StyledModalFooter {...{ footer }} className={className}>
      {footer || (
        <StyledButtonContainer
          isColumnButtonsOnMobile={isColumnButtonsOnMobile}
        >
          {cancelButtonText ? (
            <Button
              disabled={isLoading || cancelButtonProps?.disabled}
              {...cancelButtonProps}
              fullWidth={isColumnButtonsOnMobile}
              variant="secondary"
              onClick={handleCancel}
            >
              {cancelButtonText}
            </Button>
          ) : null}
          {okButtonText ? (
            <Button
              disabled={isLoading || okButtonProps?.disabled}
              {...okButtonProps}
              fullWidth={isColumnButtonsOnMobile}
              variant="primary"
              onClick={handleOk}
            >
              {okButtonText}
            </Button>
          ) : null}
        </StyledButtonContainer>
      )}
    </StyledModalFooter>
  )
}
