import { useEffect, useState } from "react"
import uniqueId from "lodash/uniqueId"

import { calculateBounds } from "../utils"

import { UseModalDraggable } from "../ModalTypes"

export const useModalDraggable: UseModalDraggable = ({
  isDraggable,
  isSmallDesktopScreen,
}) => {
  const [draggableHandleKey, setDraggableHandleKey] = useState(null)
  const [bounds, setBounds] = useState({
    left: 0,
    top: 0,
    bottom: 0,
    right: 0,
  })

  useEffect(() => {
    setDraggableHandleKey(uniqueId("modal-").toString())
  }, [])

  const disabledDrag = !isDraggable || isSmallDesktopScreen
  const handleMove = (event, uiData, draggableRef) => {
    setBounds(calculateBounds(uiData, draggableRef))
  }

  return {
    handleMove,
    bounds,
    disabledDrag,
    draggableHandleKey,
  }
}
