import { useMemo } from "react"

import {
  BODY_COLUMN_KEYS,
  COLUMN_MIN_WIDTH,
  EDIT_FORM_KEYS,
  FOOTER_COLUMN_KEYS,
  HEADER_COLUMN_KEYS,
} from "components/Table/constants"
import {
  BodyColumn,
  Column,
  ColumnWidths,
  EditFormItem,
  FooterColumn,
  HeaderColumn,
  TableBaseData,
} from "components/Table/TableTypes"
import {
  buildDefaultValues,
  checkIsFunction,
  extractFromObjectByKeys,
  getObjectKeys,
  populateTargetKeys,
} from "utils"
import { COMMON_FILTER_KEYS } from "consts"
import { FieldSetting } from "types"

import { UseColumnsParams, UseColumnsReturn } from "./UseColumnsTypes"

export const useColumns = <DataType extends TableBaseData, FilterValuesType>({
  columns: columnsParam,
  settings: settingsParam,
  shouldPreserveHiddenColumnUrlParams,
  rowSpanMax,
}: UseColumnsParams<DataType, FilterValuesType>): UseColumnsReturn<
  DataType,
  FilterValuesType
> => {
  return useMemo(() => {
    const columns = columnsParam || []
    const settings = settingsParam || []

    const columnWidths: ColumnWidths = []
    const columnMinWidths: ColumnWidths = []
    const headerColumns: Array<HeaderColumn<FilterValuesType>> = []
    const bodyColumns: Array<BodyColumn<DataType>> = []
    const footerColumns: Array<FooterColumn> = []
    let hasFooterColumns = false
    const filters: Array<FieldSetting> = []
    const editFormItems: Array<EditFormItem<DataType>> = []
    const targetKeys: Array<string> = []
    const numberKeys: Array<string> = [
      COMMON_FILTER_KEYS.page,
      COMMON_FILTER_KEYS.pageSize,
    ]
    const dateKeys: Array<string> = []
    const dateTimeKeys: Array<string> = []
    const dateRangeKeys: Array<string> = []
    const selectWithSearchKeys: Array<string> = []
    const hiddenKeys: Set<string> = new Set()
    const persistOnClearKeys: Array<string> = getObjectKeys(COMMON_FILTER_KEYS)
    let hasColumnsWidthDifferentRowSpan: boolean = false

    settings?.forEach((setting) => {
      const isHiddenFilter: boolean =
        shouldPreserveHiddenColumnUrlParams && !setting.value

      if (isHiddenFilter) {
        hiddenKeys.add(setting.name)
      }

      const isSkipped: boolean =
        !shouldPreserveHiddenColumnUrlParams && !setting.value

      // Skip any processing if the column is hidden in settings and the URL params should not be preserved
      if (isSkipped) {
        return
      }

      const column = columns.find(
        (columnItem) => columnItem.key === setting.name
      )

      if (!column) {
        return
      }

      // Constructing filter settings to populate the search form with default values
      // This operation is done even if the column is hidden in settings, the filter values should be populated
      if (column.type !== "default") {
        const item: FieldSetting = {
          name: column.filterKey,
          type: column.type,
          inputProps: column.inputProps,
        } as FieldSetting

        filters.push(item)

        populateTargetKeys({
          item,
          targetKeys,
          numberKeys,
          dateKeys,
          dateTimeKeys,
          dateRangeKeys,
          selectWithSearchKeys,
        })

        if (column.shouldPersistOnReset) {
          persistOnClearKeys.push(column.filterKey)
        }
      }

      // If the column is hidden in settings, the rest of the operations are skipped
      if (!setting.value) {
        return
      }

      hasColumnsWidthDifferentRowSpan =
        hasColumnsWidthDifferentRowSpan ||
        (!!column.rowSpan && column.rowSpan !== rowSpanMax)

      const width: number = setting.width || column.width
      const minWidth: number =
        setting.minWidth || column.minWidth || COLUMN_MIN_WIDTH
      const widthProcessed: number = width < minWidth ? minWidth : width

      columnWidths.push(widthProcessed)
      columnMinWidths.push(minWidth)

      // Constructing the header columns, including only the filters needed for the header
      const headerColumnItem = extractFromObjectByKeys<
        Column<DataType, FilterValuesType>,
        HeaderColumn<FilterValuesType>
      >({
        keys: HEADER_COLUMN_KEYS,
        object: column,
      })

      headerColumns.push(headerColumnItem)

      // Constructing the body columns, including only the filters needed for the body
      const bodyColumnItem = extractFromObjectByKeys<
        Column<DataType, FilterValuesType>,
        BodyColumn<DataType>
      >({
        keys: BODY_COLUMN_KEYS,
        object: column,
      })

      bodyColumns.push({
        ...bodyColumnItem,
        hasEditDropdown: checkIsFunction(column.renderEditForm),
      })

      const footerColumnItem = extractFromObjectByKeys<
        Column<DataType, FilterValuesType>,
        FooterColumn
      >({
        keys: FOOTER_COLUMN_KEYS,
        object: column,
      })

      if (footerColumnItem.footerCell) {
        hasFooterColumns = true
      }

      footerColumns.push(footerColumnItem)

      const editFormItem = extractFromObjectByKeys<
        Column<DataType, FilterValuesType>,
        EditFormItem<DataType>
      >({
        keys: EDIT_FORM_KEYS,
        object: column,
      })

      editFormItems.push(editFormItem)
    })

    return {
      headerColumns,
      bodyColumns,
      footerColumns: hasFooterColumns ? footerColumns : undefined,
      columnWidths,
      columnMinWidths,
      filterDefaultValues: buildDefaultValues<FilterValuesType>(filters),
      editFormItems,
      targetKeys,
      numberKeys,
      dateKeys,
      dateTimeKeys,
      dateRangeKeys,
      selectWithSearchKeys,
      hiddenKeys,
      persistOnClearKeys,
      rowSpanMax: hasColumnsWidthDifferentRowSpan ? rowSpanMax : 1,
    }
  }, [columnsParam, settingsParam, rowSpanMax])
}
