import React, { useMemo } from "react"

import { LABELS_DEFAULT } from "components/Table/constants"

import { LabelsContext } from "./LabelsContext"

import { LabelsProviderProps } from "./LabelsProviderTypes"

export const LabelsProvider = ({ children, labels }: LabelsProviderProps) => {
  const value = useMemo(() => {
    return { ...LABELS_DEFAULT, ...labels }
  }, [
    // Bulk edit labels
    labels.selectAll,
    labels.selectAllOnPage,
    labels.unselectAll,
    // Table resize labels
    labels.resize,
    labels.closeTableResize,
    labels.resetTableResize,
    labels.saveTableResize,
    labels.resizeIconPopover,
    // Table content labels
    labels.content,
    labels.applyTableContent,
    labels.resetTableContent,
    // Sorting labels
    labels.asc,
    labels.desc,
    // Pagination labels
    labels.goToLabel,
    labels.inputPageLabel,
    labels.prevPageTitle,
    labels.nextPageTitle,
  ])

  return (
    <LabelsContext.Provider value={value}>{children}</LabelsContext.Provider>
  )
}
