import React, { useC<PERSON>back, useContext, useMemo, useState } from "react"

import {
  Box,
  Button,
  Checkbox,
  Icon,
  IconPopover,
  Modal,
  Popover,
  SortableList,
  Typography,
} from "components"
import { IconNames } from "components/Icon/IconTypes"
import { LabelsContext } from "components/Table/components/LabelsProvider"
import { ManagementContext } from "components/Table/components/ManagementProvider"
import { useEnterAndEscapeKeyDown, useToggle, useUpdateIfChanged } from "hooks"
import { checkIsArray } from "utils"
import { Colors } from "types"

import { TableContentProps } from "./TableContentTypes"

export const TableContent = ({
  isFullSize,
  isLoading,
  buttonSize,
  tableContentModalAddon,
  hasActiveHiddenFilters,
  activeFilters,
  tableContentDefaultSettings,
}: TableContentProps) => {
  const {
    content,
    resetTableContent,
    applyTableContent,
    hiddenFiltersPopover,
    hiddenFiltersColumnPopover,
  } = useContext(LabelsContext)

  const {
    tableContentSettings,
    tableContentSettingsHiddenKeys,
    onSaveContent,
  } = useContext(ManagementContext)

  const { open, handleOpen, handleClose } = useToggle()

  const [settings, setSettings] = useState(tableContentSettings)

  const items = useMemo(() => {
    if (!checkIsArray(settings)) {
      return []
    }

    return settings.map((item) => ({
      label: item.label,
      value: item.name,
    }))
  }, [settings])

  useUpdateIfChanged({
    values: tableContentSettings,
    updateValues: setSettings,
  })

  const buildHandleCheck = useCallback(
    (name: string) =>
      (isChecked: boolean): void => {
        setSettings((prevSettings) => {
          return prevSettings.map((setting) => {
            if (setting.name === name) {
              return {
                ...setting,
                value: isChecked,
              }
            }

            return setting
          })
        })
      },
    []
  )

  const handleSort = useCallback(
    (itemsNew): void => {
      const settingsNew = itemsNew.map(({ value }) => {
        return settings.find((setting) => setting.name === value)
      })

      setSettings(settingsNew)
    },
    [settings]
  )

  const isItemChecked = useCallback(
    (name: string): boolean => {
      return settings.some((setting) => setting.name === name && setting.value)
    },
    [settings]
  )

  const handleApply = useCallback((): void => {
    onSaveContent({
      settings,
      closeModal: handleClose,
    })
  }, [onSaveContent, settings, handleClose])

  const handleCancel = useCallback((): void => {
    setSettings(tableContentSettings)
    handleClose()
  }, [tableContentSettings])

  const handleReset = useCallback((): void => {
    if (!checkIsArray(tableContentDefaultSettings)) {
      return
    }

    setSettings((settingsPrev) => {
      return tableContentDefaultSettings.map((setting) => {
        const settingPrev = settingsPrev.find(
          (settingItem) => settingItem.name === setting.name
        )

        const { width } = settingPrev || {}

        if (!width) {
          return setting
        }

        return {
          ...setting,
          width,
        }
      })
    })
  }, [tableContentDefaultSettings])

  useEnterAndEscapeKeyDown({
    isPressEnterDisabled: !open,
    isPressEnterDefaultPrevented: true,
    onPressEnter: handleApply,
    isPressEscapeDefaultPrevented: true,
    onPressEscape: handleCancel,
  })

  const icon: IconNames = hasActiveHiddenFilters ? "icnWarning" : "icnSetting"
  const iconBadgeColor: Colors = hasActiveHiddenFilters
    ? "--color-icon-warning"
    : undefined

  const fullSizePopoverContent: string = hasActiveHiddenFilters
    ? hiddenFiltersPopover
    : null

  const popoverContent: string = hasActiveHiddenFilters
    ? hiddenFiltersPopover
    : content

  return (
    <>
      {isFullSize ? (
        <Popover isFixed content={fullSizePopoverContent} placement="topRight">
          <Button
            disabled={isLoading}
            icon={icon}
            iconBadgeColor={iconBadgeColor}
            minWidth="min-content"
            variant="secondary"
            onClick={handleOpen}
          >
            {content}
          </Button>
        </Popover>
      ) : (
        <Popover isFixed content={popoverContent} placement="topRight">
          <Button
            iconOnly
            disabled={isLoading}
            icon={icon}
            iconBadgeColor={iconBadgeColor}
            size={buttonSize}
            onClick={handleOpen}
          />
        </Popover>
      )}
      {open ? (
        <Modal
          isWithoutBodyPadding
          visible
          title={content}
          width="--modal-size-xs"
          footer={
            <Box
              gap="m"
              width="100%"
              mLG={{
                display: "grid",
                gridTemplateColumns: "repeat(2, 1fr)",
              }}
              mSM={{
                display: "flex",
                justify: "end",
              }}
            >
              <Button onClick={handleReset}>{resetTableContent}</Button>
              <Button onClick={handleApply}>{applyTableContent}</Button>
            </Box>
          }
          onCancel={handleCancel}
        >
          <Box
            flexDirection="column"
            height="min-content"
            maxHeight="calc(100vh - 2 * var(--margin-xl) - 2 * var(--modal-height-m))"
          >
            {tableContentModalAddon ? (
              <Box display="block" hasBorder={{ bottom: true }}>
                {tableContentModalAddon}
              </Box>
            ) : null}

            <Box
              display="block"
              overflowY="auto"
              paddingLeft="l"
              paddingRight="l"
            >
              <SortableList
                items={items}
                renderItem={({ item }) => {
                  const key = String(item.value)

                  const isActiveAndHidden: boolean =
                    activeFilters.has(key) &&
                    tableContentSettingsHiddenKeys.has(key)

                  return (
                    <Box
                      align="center"
                      gap="m"
                      hasBorder={{ bottom: true }}
                      justify="space-between"
                      padding="m"
                      width="100%"
                    >
                      <Box align="center" gap="m">
                        <Checkbox
                          checked={isItemChecked(String(item.value))}
                          onChange={buildHandleCheck(String(item.value))}
                        />
                        <Typography variant="--font-body-text-7">
                          {item.label}
                        </Typography>
                      </Box>
                      <Box align="center" gap="m">
                        {isActiveAndHidden ? (
                          <IconPopover
                            color="--color-icon-warning"
                            content={hiddenFiltersColumnPopover}
                            name="icnWarning"
                            size="--icon-size-3"
                          />
                        ) : null}
                        <Icon
                          color="--color-icon-static"
                          name="icnDrag"
                          size="--icon-size-5"
                        />
                      </Box>
                    </Box>
                  )
                }}
                onSort={handleSort}
              />
            </Box>
          </Box>
        </Modal>
      ) : null}
    </>
  )
}
