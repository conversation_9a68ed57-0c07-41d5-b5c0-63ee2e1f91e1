import React, { useMemo, useState } from "react"

import { Id } from "components/Table/TableTypes"
import { checkIsArray } from "utils"

import { BulkChangesContext } from "./BulkChangesContext"

import { BulkChangesContextType } from "./BulkChangesProviderTypes"

export const BulkChangesProvider = ({
  children,
  allIds,
  totalCount,
  bulkActions,
  isControlPressed,
  isShiftPressed,
}) => {
  const [selectedIds, setSelectedIds] = useState<Set<Id>>()

  const [isAllSelected, setIsAllSelected] = useState(false)

  const value: BulkChangesContextType = useMemo(() => {
    const buildHandleSelect =
      (id: Id) =>
      (isChecked: boolean): void => {
        // Reset isAllSelected to false if a single checkbox is checked
        setIsAllSelected(false)
        // Update selectedIds
        setSelectedIds((prevState) => {
          const newState = new Set(prevState)

          if (isChecked) {
            newState.add(id)
          } else {
            newState.delete(id)
          }

          return newState
        })
      }

    const handleSelectAllOnPage = (): void => {
      // Reset isAllSelected.
      setIsAllSelected(false)
      // Select all ids in current dataset (current page).
      setSelectedIds(allIds)
    }

    const handleSelectAll = (): void => {
      // Reset selectedIds.
      setSelectedIds(new Set())
      // Always set isAllSelected to true when the user selects that option.
      setIsAllSelected(true)
    }

    const handleUnSelectAll = (): void => {
      setSelectedIds(new Set())
      setIsAllSelected(false)
    }

    let selectedCount = 0

    if (isAllSelected) {
      selectedCount = totalCount
    }

    if (selectedIds?.size > 0) {
      selectedCount = selectedIds.size
    }

    return {
      isAllSelected,
      isAllOnPageSelected: selectedIds?.size === allIds.size,
      selectedIds,
      selectedCount,
      buildOnSelect: buildHandleSelect,
      onSelectAllOnPage: handleSelectAllOnPage,
      onSelectAll: handleSelectAll,
      onUnSelectAll: handleUnSelectAll,
      bulkActions,
      isControlPressed,
      isShiftPressed,
      hasBulkActions: checkIsArray(bulkActions),
    }
  }, [
    isAllSelected,
    selectedIds,
    allIds,
    totalCount,
    bulkActions,
    isControlPressed,
    isShiftPressed,
  ])

  return (
    <BulkChangesContext.Provider value={value}>
      {children}
    </BulkChangesContext.Provider>
  )
}
