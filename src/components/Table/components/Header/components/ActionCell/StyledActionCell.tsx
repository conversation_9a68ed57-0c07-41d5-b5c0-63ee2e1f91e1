import styled, { css, FlattenSimpleInterpolation } from "styled-components"

import { StyledActionCellProps } from "./ActionCellTypes"

const buildPaddingStyles = ({
  hasBulkActions,
}: StyledActionCellProps): FlattenSimpleInterpolation => {
  const padding = !hasBulkActions ? "var(--padding-l)" : "var(--padding-m)"

  return css`
    padding-left: ${padding};
  `
}

export const buildStickyStyles = ({
  isSticky,
}: StyledActionCellProps): FlattenSimpleInterpolation => {
  if (!isSticky) {
    return
  }

  return css`
    position: sticky;
    left: 0;
    z-index: 1;
    box-shadow: inset -1px -1px var(--color-border-main) !important;
  `
}

export const StyledActionCell = styled.th<StyledActionCellProps>`
  padding: var(--padding-m) var(--padding-s);
  width: 0;
  ${buildPaddingStyles};
  ${buildStickyStyles};

  > div {
    width: min-content;
    display: flex;
    gap: var(--gap-m);
    align-items: center;
    justify-content: center;
    flex-wrap: nowrap;
  }
`
