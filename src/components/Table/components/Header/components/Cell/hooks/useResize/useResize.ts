import { MouseEvent, useCallback, useEffect, useRef, useState } from "react"

import { convertToDimension } from "utils"

import { UseResize } from "./UseResizeTypes"

export const useResize: UseResize = ({
  onResizeEnd,
  width,
  minWidth,
  isDisabled,
  index,
}) => {
  const cellRef = useRef<HTMLTableCellElement>(null)

  const [mouseXAtMouseDown, setMouseXAtMouseDown] = useState(null)
  const [widthAtMouseDown, setWidthAtMouseDown] = useState(null)
  const [isResizeInProgress, setIsResizeInProgress] = useState(false)

  const setWidths = useCallback(
    (widthNew: number | string) => {
      if (!cellRef.current) {
        return
      }

      if (widthNew < minWidth) {
        return
      }

      const widthStyle: string = convertToDimension(widthNew)

      cellRef.current.style.width = widthStyle
      cellRef.current.style.minWidth = widthStyle
      cellRef.current.style.maxWidth = widthStyle
    },
    [index, minWidth]
  )

  useEffect(() => {
    setWidths(width)
  }, [width, setWidths])

  useEffect(() => {
    const isSkipped: boolean =
      mouseXAtMouseDown === null || widthAtMouseDown === null || isDisabled

    if (isSkipped) {
      return
    }

    const handleMouseMove = (event): void => {
      const dX = event.clientX - mouseXAtMouseDown
      const newWidth = widthAtMouseDown + dX

      setWidths(newWidth)
    }

    const handleMouseUp = (event): void => {
      setMouseXAtMouseDown(null)
      setWidthAtMouseDown(null)
      setIsResizeInProgress(false)

      const dX = event.clientX - mouseXAtMouseDown
      const newWidth = widthAtMouseDown + dX

      setWidths(newWidth)
      onResizeEnd(newWidth)
    }

    document.addEventListener("mousemove", handleMouseMove)
    document.addEventListener("mouseup", handleMouseUp)

    return () => {
      document.removeEventListener("mousemove", handleMouseMove)
      document.removeEventListener("mouseup", handleMouseUp)
    }
  }, [mouseXAtMouseDown, widthAtMouseDown])

  const handleMouseDown = (event: MouseEvent): void => {
    if (isDisabled) {
      return
    }

    setMouseXAtMouseDown(event.clientX)
    setWidthAtMouseDown(width)
    setIsResizeInProgress(true)
  }

  return {
    cellRef,
    onMouseDown: handleMouseDown,
    isResizeInProgress,
  }
}
