import React, { useContext } from "react"

import { Field } from "components"
import { LabelsContext } from "components/Table/components/LabelsProvider"
import { InputItemOfType, InputPropsOfType } from "types/InputTypes"

import type { FilterProps } from "./FilterTypes"

export const Filter = <FilterValuesType,>({
  column,
  isLoading,
}: FilterProps<FilterValuesType>) => {
  const { asc, desc } = useContext(LabelsContext)

  const { title, filterKey, sorter, type, inputProps } = column

  if (type === "default") {
    return null
  }

  const isDisabled: boolean = isLoading || inputProps?.isDisabled

  const injectedInputProps = {
    label: title,
    isDisabled,
    isMultiLine: false,
    isGlobal: true,
    hasLabelTooltip: true,
    hasClearIcon: !column.shouldPersistOnReset,
  }

  const inputPropsProcessed: InputPropsOfType<typeof type> = inputProps
    ? {
        ...inputProps,
        ...injectedInputProps,
      }
    : injectedInputProps

  const props = {
    inputProps: inputPropsProcessed,
    type,
  } as InputItemOfType<typeof type>

  return (
    <Field
      name={filterKey}
      sortFieldName={sorter ? "sort" : undefined}
      sortLabels={{ asc, desc }}
      {...props}
    />
  )
}
