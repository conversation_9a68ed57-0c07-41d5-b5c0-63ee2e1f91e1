import React, { useContext } from "react"

import { TableBaseData } from "components/Table/TableTypes"

import { ActionCell, Cell } from "./components"

import { BulkChangesContext } from "../BulkChangesProvider"

import { HeaderProps } from "./HeaderTypes"

export const Header = <DataType extends TableBaseData, FilterValuesType>({
  columns,
  actionColumn,
  hasActiveFilters,
  columnWidths,
  columnMinWidths,
  isLoading,
  handleClear,
}: HeaderProps<DataType, FilterValuesType>) => {
  const { hasBulkActions } = useContext(BulkChangesContext)

  return (
    <tr>
      <ActionCell<DataType>
        column={actionColumn}
        handleClear={handleClear}
        hasActiveFilters={hasActiveFilters}
        isLoading={isLoading}
      />

      {columns?.map((column, index) => {
        return (
          <Cell<FilterValuesType>
            key={column.key}
            column={column}
            hasBulkActions={hasBulkActions}
            index={index}
            isLoading={isLoading}
            minWidth={columnMinWidths[index]}
            width={columnWidths[index]}
          />
        )
      })}
    </tr>
  )
}
