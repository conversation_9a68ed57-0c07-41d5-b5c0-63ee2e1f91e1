import React from "react"

import { useRowClick } from "components/Table/hooks"

import { RowWrapperProps } from "./RowWrapperTypes"

export const RowWrapper = ({
  children,
  isRowSelected,
  id,
  allIdsArray,
  index,
}: RowWrapperProps) => {
  const handleClick = useRowClick({
    id,
    allIdsArray,
  })

  return (
    <tr
      data-index={index}
      data-is-selected={isRowSelected}
      data-item-id={id}
      onClick={handleClick}
    >
      {children}
    </tr>
  )
}
