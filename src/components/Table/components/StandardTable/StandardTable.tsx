import React, { <PERSON>actNode, useContext } from "react"

import {
  <PERSON>erRow,
  Header,
  Row,
  StyledTableComponent,
} from "components/Table/components"
import { EmptyTable } from "components/Table/components/EmptyTable"
import { TableBaseData, TableComponentProps } from "components/Table/TableTypes"
import { checkIsArray, checkIsFunction } from "utils"

import { RowWrapper } from "./components"

import { BulkChangesContext } from "../BulkChangesProvider"

export const StandardTable = <
  DataType extends TableBaseData,
  FilterValuesType
>({
  id,
  data,
  actionColumn,
  rowSpanMax,
  hasActiveFilters,
  headerColumns,
  bodyColumns,
  footerColumns,
  columnWidths,
  columnMinWidths,
  allIdsArray,
  isLoading,
  handleClear,
  renderCustomExpandedRow,
  noDataPlaceholder,
  hasStickyHeader,
  shouldDisplayDataTable,
  shouldDisplayNoDataPlaceholder,
  columnsCount,
  bordersVariant,
}: TableComponentProps<DataType, FilterValuesType>) => {
  const { selectedIds, isAllSelected } = useContext(BulkChangesContext)

  return (
    <StyledTableComponent hasStickyHeader={hasStickyHeader} id={id}>
      <thead>
        <Header<DataType, FilterValuesType>
          actionColumn={actionColumn}
          columnMinWidths={columnMinWidths}
          columns={headerColumns}
          columnWidths={columnWidths}
          handleClear={handleClear}
          hasActiveFilters={hasActiveFilters}
          isLoading={isLoading}
        />
      </thead>
      {shouldDisplayDataTable ? (
        <tbody>
          {data.map((item, index) => {
            const rowKeyProcessed = `${item.id}-${index % rowSpanMax}`

            const isRowSelected: boolean =
              selectedIds?.has(item.id) || isAllSelected

            const customExpandedRow: ReactNode =
              checkIsFunction(renderCustomExpandedRow) &&
              !!item.tableRowMetaData?.isLastCopy
                ? renderCustomExpandedRow(item)
                : null

            return (
              <>
                <RowWrapper
                  key={rowKeyProcessed}
                  allIdsArray={allIdsArray}
                  id={item.id}
                  index={index}
                  isRowSelected={isRowSelected}
                >
                  <Row<DataType, FilterValuesType>
                    actionColumn={actionColumn}
                    bordersVariant={bordersVariant}
                    columns={bodyColumns}
                    columnWidths={columnWidths}
                    index={index}
                    item={data[index]}
                    rowSpanMax={rowSpanMax}
                  />
                </RowWrapper>

                {customExpandedRow}
              </>
            )
          })}
        </tbody>
      ) : null}

      {shouldDisplayNoDataPlaceholder ? (
        <EmptyTable
          columnsCount={columnsCount}
          noDataPlaceholder={noDataPlaceholder}
        />
      ) : null}

      {checkIsArray(footerColumns) ? (
        <tfoot>
          <FooterRow footerColumns={footerColumns} />
        </tfoot>
      ) : null}
    </StyledTableComponent>
  )
}
