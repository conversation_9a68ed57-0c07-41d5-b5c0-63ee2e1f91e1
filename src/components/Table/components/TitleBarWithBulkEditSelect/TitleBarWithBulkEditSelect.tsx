import React, { useContext, useMemo } from "react"

import { TitleBar } from "components/TitleBar"
import { checkIsArray } from "utils"

import { BulkChangesContext } from "../BulkChangesProvider"
import { BulkEditSelect } from "../BulkEditSelect"

import { TitleBarWithBulkEditSelectProps } from "./TitleBarWithBulkEditSelectTypes"

export const TitleBarWithBulkEditSelect = ({
  contents: contentsProp,
  renderSelectedCount,
  onBack,
}: TitleBarWithBulkEditSelectProps) => {
  const { bulkActions, selectedCount } = useContext(BulkChangesContext)

  const backgroundColor =
    selectedCount > 0 ? "--color-background-second" : "--color-main-background"

  const contents = useMemo(() => {
    return contentsProp?.map((content, index) => {
      const hasBulkEditSelect: boolean =
        index === 0 && checkIsArray(bulkActions)

      if (hasBulkEditSelect) {
        return (
          <BulkEditSelect
            key={index}
            bulkActions={bulkActions}
            renderSelectedCount={renderSelectedCount}
            title={content}
          />
        )
      }

      return content
    })
  }, [contentsProp, bulkActions, renderSelectedCount])

  return (
    <TitleBar
      backgroundColor={backgroundColor}
      contents={contents}
      hasBorderTop={false}
      onBack={onBack}
    />
  )
}
