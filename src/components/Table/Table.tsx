import React, { memo, useMemo, useRef } from "react"
import uniqueId from "lodash/uniqueId"
import { FormProvider } from "react-hook-form"

import { Box } from "components/Box"
import { useFilters, useResizeObserver } from "hooks"
import { checkIsArray, checkIsFunction } from "utils"

import {
  BulkChangesProvider,
  EditEntryProvider,
  EditFormWrapper,
  FooterBar,
  LabelsProvider,
  ManagementProvider,
  StandardTable,
  TitleBarWithBulkEditSelect,
  VirtualizedTable,
} from "./components"
import {
  useColumns,
  useDataAdjustedForRowSpan,
  useSettings,
  useShiftAndControlPress,
} from "./hooks"
import { renderSelectedCountDefault } from "./utils"
import {
  DEBOUNCE_TIME_DEFAULT,
  DEFAULT_FILTER_VALUES,
  FOOTER_BOX_PROPS_MAP,
  LABELS_DEFAULT,
  MAIN_BOX_COMMON_PROPS,
  ROW_SPAN_MAX_DEFAULT,
  TABLE_BORDERS_VARIANT,
  TABLE_BOX_COMMON_PROPS,
  TABLE_VARIANTS,
} from "./constants"

import { CommonStatesProvider } from "./components/CommonStatesProvider"
import { StyledTable } from "./StyledTable"

import { TableBaseData, TableBordersVariant, TableProps } from "./TableTypes"

const TableRaw = <DataType extends TableBaseData, FilterValuesType>({
  // ID of the table element to handle it via DOM API
  id: idProp,
  // Filters
  filterValues,
  filterDefaultValues,
  filterInjectedValues,
  urlSearchDefaultValue,
  onSearch,
  isConnectedToUrl = true,
  parseFilterValuesFromUrl,
  normalizeFilterValuesToUrl,
  shouldPersistInjectedValuesOnClear = true,
  resetFiltersWatchList: resetFiltersWatchListParam,
  // Data
  data: dataProp,
  isLoading = false,
  noDataPlaceholder,
  // Columns
  columns: columnsProp,
  actionColumn,
  settings: settingsProp,
  tableContentSettings,
  tableContentDefaultSettings,
  // Common props
  totalCount,
  rowSpanMax: rowSpanMaxProp = ROW_SPAN_MAX_DEFAULT,
  debounceTime = DEBOUNCE_TIME_DEFAULT,
  filtersPrefix,
  variant = TABLE_VARIANTS.fullPage,
  bordersVariant: bordersVariantProp = TABLE_BORDERS_VARIANT.half,
  isVirtualized = false,
  hasStickyHeader = false,
  shouldPreserveHiddenColumnUrlParams = true,
  isPrimaryRow = (index) => index % 2 === 1,
  // Callback that is called when the end of the list is reached
  onEndReached,
  // TitleBar
  titleBar = [null, null, null],
  onBack,
  // Bulk
  renderSelectedCount = renderSelectedCountDefault,
  bulkActions,
  // Management
  canSaveAsDefaultSettings = false,
  onChangeSettings,
  tableContentModalAddon,
  // Footer
  copyright,
  hasPageSizeSelect = true,
  customPageSize,
  // TableLabels
  labels = LABELS_DEFAULT,
  // Side bars
  leftSideBar,
  rightSideBar,
  getExpandedRowData,
  renderCustomExpandedRow,
  // Container props
  mainBoxProps,
  tableBoxProps,
  footerBoxProps,
}: TableProps<DataType, FilterValuesType>) => {
  const id = useMemo(() => {
    if (idProp) {
      return idProp
    }

    return uniqueId("table-")
  }, [idProp])

  const settingsContext = useSettings({
    settings: settingsProp,
    tableContentSettings,
    onChangeSettings,
    canSaveAsDefaultSettings,
  })

  const {
    headerColumns,
    bodyColumns,
    footerColumns,
    columnWidths,
    columnMinWidths,
    filterDefaultValues: filterDefaultValuesCalculated,
    editFormItems,
    targetKeys,
    numberKeys,
    dateKeys,
    dateTimeKeys,
    dateRangeKeys,
    selectWithSearchKeys,
    hiddenKeys,
    persistOnClearKeys,
    rowSpanMax,
  } = useColumns({
    columns: columnsProp,
    settings: settingsContext.settings,
    shouldPreserveHiddenColumnUrlParams,
    rowSpanMax: rowSpanMaxProp,
  })

  const resetFiltersWatchList: Array<string> = useMemo(() => {
    if (!checkIsArray(resetFiltersWatchList)) {
      return headerColumns.map(({ key }) => key)
    }

    return resetFiltersWatchListParam
  }, [resetFiltersWatchListParam, headerColumns])

  const { data, allIdsArray, allIds } = useDataAdjustedForRowSpan<DataType>({
    data: dataProp,
    rowSpanMax,
    isLoading,
    getExpandedRowData,
  })

  const {
    form: filtersForm,
    handleClear,
    hasActiveFilters,
    hasActiveHiddenFilters,
    activeFilters,
  } = useFilters<FilterValuesType>({
    values: filterValues,
    defaultValues: {
      ...DEFAULT_FILTER_VALUES,
      ...filterDefaultValuesCalculated,
      ...filterDefaultValues,
    },
    injectedValues: filterInjectedValues,
    urlSearchDefaultValue,
    onSearch,
    prefix: filtersPrefix,
    debounceTime,
    parseFilterValuesFromUrl,
    normalizeFilterValuesToUrl,
    isConnectedToUrl,
    targetKeys,
    numberKeys,
    dateKeys,
    dateTimeKeys,
    dateRangeKeys,
    selectWithSearchKeys,
    hiddenKeys,
    persistOnClearKeys,
    shouldPersistInjectedValuesOnClear,
    resetFiltersWatchList,
  })

  const { isControlPressed, isShiftPressed } = useShiftAndControlPress()

  const shouldDisplayDataTable: boolean = isLoading || checkIsArray(data)
  const shouldDisplayNoDataPlaceholder: boolean =
    !shouldDisplayDataTable && !!noDataPlaceholder

  const columnsCount: number = bodyColumns.length + 1

  const bordersVariant: TableBordersVariant =
    rowSpanMax > 1 ? TABLE_BORDERS_VARIANT.complete : bordersVariantProp

  const hasTitleBar: boolean =
    titleBar.some(Boolean) || checkIsArray(bulkActions)
  const hasManagementActions: boolean = checkIsFunction(onChangeSettings)
  const hasLeftSideBar: boolean = !!leftSideBar
  const hasRightSideBar: boolean = !!rightSideBar

  const TableComponent = useMemo(() => {
    return isVirtualized ? VirtualizedTable : StandardTable
  }, [isVirtualized])

  const gridTemplateColumnsString = useMemo(() => {
    const gridTemplateColumns = []

    if (hasLeftSideBar) {
      gridTemplateColumns.push("auto")
    }

    gridTemplateColumns.push("1fr")

    if (hasRightSideBar) {
      gridTemplateColumns.push("auto")
    }

    return gridTemplateColumns.join(" ")
  }, [hasLeftSideBar, hasRightSideBar])

  const ref = useRef<HTMLDivElement>()

  useResizeObserver({
    ref,
    isEnabled: shouldDisplayNoDataPlaceholder,
    callback: ({ contentRect }) => {
      const tableWrapperWidth = contentRect.width

      const noDataPlaceholderWrapper: HTMLDivElement =
        ref.current?.querySelector("[data-component-type=no-data-placeholder]")

      if (!noDataPlaceholderWrapper) {
        return
      }

      const shouldChangeWidth: boolean =
        noDataPlaceholderWrapper.offsetWidth !== tableWrapperWidth

      if (shouldChangeWidth) {
        noDataPlaceholderWrapper.style.width = `${tableWrapperWidth}px`
      }
    },
  })

  return (
    <EditEntryProvider>
      <CommonStatesProvider isPrimaryRow={isPrimaryRow} tableId={id}>
        <LabelsProvider labels={labels}>
          <ManagementProvider {...settingsContext}>
            <BulkChangesProvider
              allIds={allIds}
              bulkActions={bulkActions}
              isControlPressed={isControlPressed}
              isShiftPressed={isShiftPressed}
              totalCount={totalCount}
            >
              <FormProvider {...filtersForm}>
                <StyledTable {...MAIN_BOX_COMMON_PROPS} {...mainBoxProps}>
                  {hasTitleBar ? (
                    <TitleBarWithBulkEditSelect
                      contents={titleBar}
                      renderSelectedCount={renderSelectedCount}
                      onBack={onBack}
                    />
                  ) : null}

                  <Box
                    {...TABLE_BOX_COMMON_PROPS}
                    gridTemplateColumns={gridTemplateColumnsString}
                    {...tableBoxProps}
                  >
                    {leftSideBar}

                    <Box
                      ref={ref}
                      display="block"
                      height="100%"
                      overflow="auto"
                      hasBorder={{
                        left: hasLeftSideBar,
                        right: hasRightSideBar,
                      }}
                    >
                      <TableComponent<DataType, FilterValuesType>
                        actionColumn={actionColumn}
                        allIdsArray={allIdsArray}
                        bodyColumns={bodyColumns}
                        bordersVariant={bordersVariant}
                        columnMinWidths={columnMinWidths}
                        columnsCount={columnsCount}
                        columnWidths={columnWidths}
                        data={data}
                        footerColumns={footerColumns}
                        handleClear={handleClear}
                        hasActiveFilters={hasActiveFilters}
                        hasStickyHeader={hasStickyHeader}
                        headerColumns={headerColumns}
                        id={id}
                        isLoading={isLoading}
                        noDataPlaceholder={noDataPlaceholder}
                        renderCustomExpandedRow={renderCustomExpandedRow}
                        rowSpanMax={rowSpanMax}
                        shouldDisplayDataTable={shouldDisplayDataTable}
                        variant={variant}
                        shouldDisplayNoDataPlaceholder={
                          shouldDisplayNoDataPlaceholder
                        }
                        onEndReached={onEndReached}
                      />
                    </Box>

                    {rightSideBar}
                  </Box>

                  <Box {...FOOTER_BOX_PROPS_MAP[variant]} {...footerBoxProps}>
                    <FooterBar
                      activeFilters={activeFilters}
                      copyright={copyright}
                      customPageSize={customPageSize}
                      hasActiveHiddenFilters={hasActiveHiddenFilters}
                      hasManagementActions={hasManagementActions}
                      hasPageSizeSelect={hasPageSizeSelect}
                      isLoading={isLoading}
                      tableContentDefaultSettings={tableContentDefaultSettings}
                      tableContentModalAddon={tableContentModalAddon}
                      totalCount={totalCount}
                    />
                  </Box>
                </StyledTable>
              </FormProvider>
            </BulkChangesProvider>
          </ManagementProvider>
        </LabelsProvider>

        <EditFormWrapper<DataType> data={data} editFormItems={editFormItems} />
      </CommonStatesProvider>
    </EditEntryProvider>
  )
}

export const Table = memo(TableRaw) as typeof TableRaw
