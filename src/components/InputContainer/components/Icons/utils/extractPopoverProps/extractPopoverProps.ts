import { type PopoverProps } from "components"
import {
  FlagPopoverItemProps,
  IconPopoverItemProps,
} from "components/InputContainer/InputContainerTypes"

export const extractPopoverProps = (
  props: FlagPopoverItemProps | IconPopoverItemProps
): PopoverProps => {
  const {
    trigger,
    placement,
    isVisible,
    isFixed,
    updateConditions,
    placementsOrder,
    hasAutoAdjustment,
    isHiddenOnObscure,
    onTriggerObscured,
    ghostAreaWidth,
    title,
    content,
    isDarkMode,
    hasArrow,
    contentClassName,
    maxWidth,
    width,
  } = props

  return {
    trigger,
    placement,
    isVisible,
    isFixed,
    updateConditions,
    placementsOrder,
    hasAutoAdjustment,
    isHiddenOnObscure,
    onTriggerObscured,
    ghostAreaWidth,
    title,
    content,
    isDarkMode,
    hasArrow,
    contentClassName,
    maxWidth,
    width,
  }
}
