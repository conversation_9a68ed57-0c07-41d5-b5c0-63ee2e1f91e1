import styled, { css, FlattenSimpleInterpolation } from "styled-components"

import { INNER_HEIGHT_MAPPER } from "components/InputContainer/constants"

import { LabelProps } from "./LabelTypes"

const floatingLabelStyles = css`
  left: 0;
  right: 0;
  top: 0;
  height: 20px;
  margin: 0 var(--padding-s);
  transform: translate(0, -60%);
  z-index: 1;

  > div {
    color: var(--color-text-second);
    font: var(--font-body-text-11);
  }
`
const buildSizeStyles = ({ size }: LabelProps): FlattenSimpleInterpolation => {
  return css`
    height: ${INNER_HEIGHT_MAPPER[size]};
  `
}

const buildPaddingStyles = ({
  prefixIconsCount = 0,
  suffixIconsCount = 0,
}: LabelProps): FlattenSimpleInterpolation => {
  const basicPadding = 5 + 14

  return css`
    left: ${prefixIconsCount * basicPadding}px;
    right: ${suffixIconsCount * basicPadding}px;
  `
}

const buildFloatingStyles = ({
  isFloating,
}: LabelProps): FlattenSimpleInterpolation => {
  if (!isFloating) {
    return
  }

  return floatingLabelStyles
}

const buildDisabledStyles = ({
  isDisabled,
}: LabelProps): FlattenSimpleInterpolation => {
  if (isDisabled === true) {
    return css`
      user-select: none;
      cursor: not-allowed;

      > div {
        background-color: var(--color-background-disable);
        color: var(--color-text-disable);
      }
    `
  }

  return css``
}

export const StyledLabel = styled.div<LabelProps>`
  box-sizing: border-box;
  position: absolute;
  padding: 0;
  transition-property: height, transform;
  transition-duration: 0.1s;
  z-index: -1;
  display: flex;
  align-items: center;
  top: 0;
  bottom: 0;
  ${buildSizeStyles};
  ${buildPaddingStyles};

  > div {
    background-color: var(--color-main-background);
    color: var(--color-text-placeholders);
    font: var(--font-service-text-1);
    margin: 0 var(--padding-s);
    text-align: left;
    user-select: none;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    word-break: break-all;
  }

  ${buildFloatingStyles};
  ${buildDisabledStyles};
`
