import { useCallback, useEffect, useState } from "react"

import { checkIsFunction } from "utils"

import { UseFocus } from "./UseFocusTypes"

export const useFocus: UseFocus = ({
  isDisabled,
  onFocus: onFocusParam,
  onBlur: onBlurParam,
}) => {
  const [isFocused, setIsFocused] = useState(false)

  const handleFocus = useCallback(
    (event): void => {
      setIsFocused(true)
      event.preventDefault()

      if (isDisabled) {
        return
      }

      if (checkIsFunction(onFocusParam)) {
        onFocusParam(event)
      }

      const inputElement: HTMLInputElement | HTMLTextAreaElement =
        event.target?.querySelector("input") ||
        event.target?.querySelector("textarea")

      if (inputElement && !isFocused) {
        inputElement.focus()
      }
    },
    [onFocusParam, isFocused, isDisabled]
  )

  const handleBlur = useCallback(
    (event): void => {
      setIsFocused(false)

      if (checkIsFunction(onBlurParam)) {
        onBlurParam(event)
      }
    },
    [onBlurParam]
  )

  useEffect(() => {
    if (!isDisabled) {
      return
    }

    setIsFocused(false)
  }, [isDisabled])

  return {
    isFocused,
    onFocus: handleFocus,
    onBlur: handleBlur,
  }
}
