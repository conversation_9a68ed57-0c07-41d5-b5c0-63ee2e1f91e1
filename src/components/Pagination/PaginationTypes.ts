import { Breakpoints } from "types"

export type AdditionalButtonProps = {
  isActive?: boolean
  isButtonWide?: boolean
  isMobile?: boolean
}

export type PaginationProps = {
  total: number
  goToLabel?: string
  inputPageLabel?: string
  prevPageTitle?: string
  nextPageTitle?: string
  pageSizeLimit?: number
  currentPage?: number
  mobileViewBreakpoint?: Breakpoints
  hasGoToPageInput?: boolean
  isMobile?: boolean
  isDisabled?: boolean
  onPageChange?: (currentPage: number) => void
}
