import { useEffect, useState } from "react"

import { ButtonProps } from "components/Button"
import {
  BUTTON_WIDE_START_SIZE,
  DEFAULT_CURRENT_PAGE,
  DEFAULT_PAGE_SIZE_LIMIT,
} from "components/Pagination/constants"
import { getPagesArray } from "components/Pagination/utils"
import { useBreakpoint } from "hooks"
import { breakpoints } from "consts"

import { UsePagination } from "./UsePaginationTypes"

/**
 * Custom hook for pagination logic.
 * @param total - Total number of items.
 * @param pageSizeLimit - Maximum number of items per page.
 * @param currentPage - Current active page.
 * @param mobileViewBreakpoint - Breakpoint for mobile view.
 * @param hasGoToPageInput - Flag indicating whether the input for page navigation is enabled.
 * @param onPageChange - Callback function when the page changes.
 * @param isMobileProp - Flag indicating whether the component is in mobile view.
 */
export const usePagination: UsePagination = ({
  total,
  pageSizeLimit: limitProp,
  currentPage: currentPageProp,
  mobileViewBreakpoint,
  hasGoToPageInput,
  isMobile: isMobileProp,
  isDisabled: isDisabledProp,
  onPageChange,
}) => {
  const [currentPage, setCurrentPage] = useState(currentPageProp)
  const [inputPageValue, setInputPageValue] = useState(null)

  const breakpoint = useBreakpoint()

  useEffect(() => {
    setCurrentPage(
      currentPageProp < DEFAULT_CURRENT_PAGE
        ? DEFAULT_CURRENT_PAGE
        : currentPageProp
    )
  }, [currentPageProp])

  const limit: number = limitProp || DEFAULT_PAGE_SIZE_LIMIT
  const pagesLength = Math.ceil(total / limit)

  const breakpointsKeys = Object.keys(breakpoints)

  const filteredBreakpoints = breakpointsKeys.filter(
    (key) =>
      breakpointsKeys.indexOf(key) >
      breakpointsKeys.indexOf(mobileViewBreakpoint)
  )

  const isMobile = isMobileProp || filteredBreakpoints.includes(breakpoint)
  const buttonSize: ButtonProps["size"] = isMobile ? "large" : "small"

  const handleClick = (pageNumber?: number) => (): void => {
    setCurrentPage(pageNumber)
    onPageChange?.(pageNumber)
  }

  const handleInputPageSet = (value: string): void => {
    const currentValue = +value
    const isInputPageValid: boolean =
      !Number.isNaN(currentValue) &&
      currentValue >= 1 &&
      currentValue <= pagesLength

    if (!isInputPageValid) {
      return
    }

    setCurrentPage(currentValue)
    onPageChange?.(currentValue)
    setInputPageValue(null)
  }

  const handleInputPageChange = (value: string): void => {
    setInputPageValue(value)
  }

  const pagesArray: number[] = isMobile
    ? [currentPage]
    : Array.from({ length: pagesLength }, (item, index) => index + 1)

  const pages = getPagesArray({
    pagesLength,
    pagesArray,
    currentPage,
    isMobile,
  })

  const hasHiddenPages = pagesLength > 7
  const isPaginationHidden: boolean = total <= 0
  const isFirstPageSelected: boolean = currentPage === 1
  const isFirstPrevDisabled: boolean = isDisabledProp || isFirstPageSelected
  const isNavPrevDotsShow: boolean =
    currentPage > 4 && hasHiddenPages && !isMobile
  const isNavNextDotsShow: boolean =
    currentPage + 3 < pagesLength && hasHiddenPages && !isMobile
  const isLastPageSelected = currentPage === pagesLength
  const isLastNextDisabled: boolean = isDisabledProp || isLastPageSelected
  const isInputPageShow: boolean = hasGoToPageInput && !isMobile
  const isLastPageWide = pagesLength > BUTTON_WIDE_START_SIZE
  const isFirstPageShow: boolean = pagesLength > 1 && !isMobile

  return {
    currentPage,
    buttonSize,
    pages,
    pagesLength,
    inputPageValue,
    isFirstPageSelected,
    isFirstPrevDisabled,
    isNavPrevDotsShow,
    isNavNextDotsShow,
    isLastPageSelected,
    isLastNextDisabled,
    isInputPageShow,
    isPaginationHidden,
    isMobile,
    isLastPageWide,
    isFirstPageShow,
    handleClick,
    handleInputPageSet,
    handleInputPageChange,
  }
}
