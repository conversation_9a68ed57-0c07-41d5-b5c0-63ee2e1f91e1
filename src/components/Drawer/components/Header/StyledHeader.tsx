import styled, { css } from "styled-components"

import { DRAWER_HEADER_HEIGHTS } from "components/Drawer/constants"

import { StyledHeaderProps } from "./HeaderTypes"

const buildMarginStyles = ({ hasHeaderBottomMargin }: StyledHeaderProps) => {
  if (!hasHeaderBottomMargin) {
    return ""
  }

  return css`
    margin-bottom: var(--margin-m);
  `
}

const buildHeaderHeightStyles = ({ headerHeight }: StyledHeaderProps) => {
  const headerHeightStyle =
    DRAWER_HEADER_HEIGHTS[headerHeight] || DRAWER_HEADER_HEIGHTS.m

  return css`
    height: ${headerHeightStyle};
  `
}

export const StyledHeader = styled.div<StyledHeaderProps>`
  display: flex;
  gap: var(--gap-m);
  padding: var(--padding-m) var(--padding-l);
  background-color: var(--color-main-background);
  border-bottom: var(--border-main);
  ${buildMarginStyles};
  ${buildHeaderHeightStyles};

  :not(:has(h2)) {
    align-items: center;
  }

  > div {
    flex: 1;
  }
`
