import styled from "styled-components"

import { StyledContainerSpinnerProps } from "components/Spinner/types"
import { hasTipWithoutChildren } from "components/Spinner/utils/validations"

export const StyledContainerSpinner = styled.div<StyledContainerSpinnerProps>(
  ({ tip, hasChildren, size }) => ({
    display: tip ? "flex" : null,
    alignItems: tip ? "center" : null,
    flexDirection: tip ? "column" : null,
    position: hasChildren ? "absolute" : null,
    top: hasChildren ? "50%" : null,
    left: hasChildren ? "50%" : null,
    marginTop: hasTipWithoutChildren({ tip, hasChildren, size }),
    zIndex: hasChildren ? "999" : null,
  })
)
