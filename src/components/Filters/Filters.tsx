import React from "react"

import { FormItems } from "components"
import { useFilters, useTargetKeys } from "hooks"

import { FiltersProps } from "./FiltersTypes"

export const Filters = <FilterValuesType,>({
  boxContainerProps,
  gridContainerProps,
  items,
  defaultValues,
  onSearch,
  prefix,
  debounceTime,
  isConnectedToUrl,
  updateUrlSearch,
  parseFilterValuesFromUrl,
  normalizeFilterValuesToUrl,
}: FiltersProps<FilterValuesType>) => {
  const keys = useTargetKeys<FilterValuesType>({ items })

  const { form } = useFilters<FilterValuesType>({
    defaultValues,
    onSearch,
    prefix,
    debounceTime,
    isConnectedToUrl,
    updateUrlSearch,
    parseFilterValuesFromUrl,
    normalizeFilterValuesToUrl,
    ...keys,
  })

  return (
    <FormItems
      boxContainerProps={boxContainerProps}
      form={form}
      gridContainerProps={gridContainerProps}
      items={items}
    />
  )
}
