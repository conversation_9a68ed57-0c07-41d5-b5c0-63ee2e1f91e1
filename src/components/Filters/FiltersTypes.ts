import { FormItemsProps } from "components/FormItems"
import { UseFiltersParams } from "hooks/useFilters"

export type FiltersProps<FilterValuesType> = Pick<
  FormItemsProps,
  "boxContainerProps" | "gridContainerProps" | "items"
> &
  Pick<
    UseFiltersParams<FilterValuesType>,
    | "defaultValues"
    | "onSearch"
    | "prefix"
    | "debounceTime"
    | "isConnectedToUrl"
    | "updateUrlSearch"
    | "parseFilterValuesFromUrl"
    | "normalizeFilterValuesToUrl"
  >
