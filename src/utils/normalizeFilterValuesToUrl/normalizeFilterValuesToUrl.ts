import { format } from "date-fns"

import { formatDateRange } from "utils"
import { DATE_OUTPUT_FORMATS } from "consts"

import { normalizePageSize } from "../normalizePageSize"

import { NormalizeFilterValuesToUrlParams } from "./NormalizeFilterValuesToUrlTypes"

export const normalizeFilterValuesToUrl = <Type>({
  values,
  dateKeys,
  dateTimeKeys,
  dateRangeKeys,
  selectWithSearchKeys,
}: NormalizeFilterValuesToUrlParams<Type>): Record<string, string> => {
  return Object.keys(values).reduce((acc, key) => {
    const value = values[key]

    const isEmptyArray: boolean = Array.isArray(value) && value.length === 0

    const hasValue: boolean =
      value !== undefined && value !== null && value !== "" && !isEmptyArray

    if (!hasValue) {
      return acc
    }

    if (key === "pageSize") {
      const pageSizeAsNumber = Number(value)

      if (Number.isNaN(pageSizeAsNumber)) {
        return acc
      }

      acc[key] = normalizePageSize(pageSizeAsNumber)

      return acc
    }

    if (dateKeys?.includes(key)) {
      acc[key] = format(value, DATE_OUTPUT_FORMATS.SERVER_DATE_FORMAT)

      return acc
    }

    if (dateTimeKeys?.includes(key)) {
      acc[key] = format(value, DATE_OUTPUT_FORMATS.SERVER_DATE_FORMAT_WITH_TIME)

      return acc
    }

    if (selectWithSearchKeys?.includes(key)) {
      const hasSelectWithSearchValue: boolean = Array.isArray(value?.value)
        ? value?.value.length > 0
        : !!value?.value

      const hasSelectWithSearchSearchValue: boolean = !!value?.searchValue

      if (hasSelectWithSearchValue || hasSelectWithSearchSearchValue) {
        acc[`${key}_value`] = value.value
        acc[`${key}_searchValue`] = value.searchValue
      }

      return acc
    }

    const isDateRangeField = dateRangeKeys?.includes(key)
    const isDateRangeFieldWithValue: boolean =
      isDateRangeField && !!value.selected
    const isDateRangeFieldWithoutValue: boolean =
      isDateRangeField && !value.selected

    if (isDateRangeFieldWithValue) {
      acc[`${key}_selected`] = formatDateRange({
        range: value.selected,
        format: DATE_OUTPUT_FORMATS.SERVER_DATE_FORMAT,
      })
      acc[`${key}_inputMode`] = value.inputMode

      return acc
    }

    if (isDateRangeFieldWithoutValue) {
      acc[`${key}_selected`] = ""
      acc[`${key}_inputMode`] = ""

      return acc
    }

    acc[key] = value

    return acc
  }, {})
}
