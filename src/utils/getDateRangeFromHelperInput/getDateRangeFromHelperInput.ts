import {
  add,
  endOfDay,
  endOfMonth,
  endOfWeek,
  endOfYear,
  isBefore,
  max,
  min,
  startOfDay,
  startOfMonth,
  startOfWeek,
  startOfYear,
  sub,
} from "date-fns"

import { PERIODS } from "components/DateRangePicker/constants"

import type { GetDateRangeFromHelperInputParams } from "./GetDateRangeFromHelperInputTypes"

export const getDateRangeFromHelperInput = ({
  inputMode,
  fromDate,
  toDate,
  input,
}: GetDateRangeFromHelperInputParams): Interval | null => {
  const getAdjustedDateRange = (range: Interval): Interval => {
    const start = range.start ? max([startOfDay(range.start), fromDate]) : null
    const end = range.end ? min([endOfDay(range.end), toDate]) : null

    const hasStartAndHasNoEnd: boolean = !!start && !end

    if (hasStartAndHasNoEnd) {
      return { start, end: null }
    }

    if (isBefore(end, start)) {
      return null
    }

    return { start, end }
  }

  switch (inputMode) {
    case "equals": {
      if (!input) {
        return null
      }

      return getAdjustedDateRange({
        start: input,
        end: input,
      })
    }
    case "previous": {
      const { count, period } = input

      if (!count || !period) {
        return null
      }
      const today = startOfDay(new Date())
      const end = sub(today, { days: 1 })
      const pivotEnd = period === PERIODS.days ? today : end
      const start = sub(pivotEnd, { [period]: count })

      const result = {
        start: startOfDay(start),
        end: endOfDay(end),
      }

      return getAdjustedDateRange(result)
    }
    case "between": {
      return getAdjustedDateRange(input)
    }
    case "after": {
      if (!input) {
        return null
      }

      const start = add(input, { days: 1 })

      return getAdjustedDateRange({
        start,
        end: toDate,
      })
    }
    case "onOrAfter": {
      if (!input) {
        return null
      }

      return getAdjustedDateRange({
        start: input,
        end: toDate,
      })
    }
    case "before": {
      if (!input) {
        return null
      }
      const end = sub(input, { days: 1 })

      return getAdjustedDateRange({
        start: fromDate,
        end,
      })
    }
    case "onOrBefore": {
      if (!input) {
        return null
      }

      return getAdjustedDateRange({
        start: fromDate,
        end: input,
      })
    }
    case "today": {
      const today = new Date()

      return getAdjustedDateRange({ start: today, end: today })
    }
    case "yesterday": {
      const yesterday = sub(new Date(), { days: 1 })

      return getAdjustedDateRange({ start: yesterday, end: yesterday })
    }
    case "currentWeek": {
      const today = new Date()
      const start = startOfWeek(today, { weekStartsOn: 1 })
      const end = endOfWeek(today, { weekStartsOn: 1 })

      return getAdjustedDateRange({ start, end })
    }
    case "last7days": {
      const end = new Date()
      const start = sub(end, { days: 6 })

      return getAdjustedDateRange({ start, end })
    }
    case "lastWeek": {
      const today = new Date()
      const end = endOfWeek(sub(today, { weeks: 1 }), { weekStartsOn: 1 })
      const start = startOfWeek(end, { weekStartsOn: 1 })

      return getAdjustedDateRange({ start, end })
    }
    case "last14days": {
      const end = new Date()
      const start = sub(end, { days: 13 })

      return getAdjustedDateRange({ start, end })
    }
    case "currentMonth": {
      const today = new Date()
      const start = startOfMonth(today)
      const end = endOfMonth(today)

      return getAdjustedDateRange({ start, end })
    }
    case "lastMonth": {
      const today = new Date()
      const previousMonth = sub(today, { months: 1 })
      const start = startOfMonth(previousMonth)
      const end = endOfMonth(previousMonth)

      return getAdjustedDateRange({ start, end })
    }
    case "last30days": {
      const end = new Date()
      const start = sub(end, { days: 29 })

      return getAdjustedDateRange({ start, end })
    }
    case "last90days": {
      const end = new Date()
      const start = sub(end, { days: 89 })

      return getAdjustedDateRange({ start, end })
    }
    case "last6months": {
      const today = new Date()
      const previousMonth = sub(today, { months: 1 })
      const sixMonthsEarlier = sub(today, { months: 6 })
      const start = startOfMonth(sixMonthsEarlier)
      const end = endOfMonth(previousMonth)

      return getAdjustedDateRange({ start, end })
    }
    case "currentYear": {
      const today = new Date()
      const start = startOfYear(today)
      const end = endOfYear(today)

      return getAdjustedDateRange({ start, end })
    }
    case "lastYear": {
      const today = new Date()
      const previousYear = sub(today, { years: 1 })
      const start = startOfYear(previousYear)
      const end = endOfYear(previousYear)

      return getAdjustedDateRange({ start, end })
    }
    case "allTime": {
      return getAdjustedDateRange({ start: fromDate, end: toDate })
    }
    default: {
      return null
    }
  }
}
