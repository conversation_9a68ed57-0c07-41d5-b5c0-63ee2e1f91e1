import { AddPrefixToObjectKeysParams } from "./AddPrefixToObjectKeysTypes"

export const addPrefixToObjectKeys = <InputType>({
  values,
  prefix,
}: AddPrefixToObjectKeysParams<InputType>): Record<string, string> => {
  if (!prefix) {
    return values as Record<string, string>
  }

  return Object.keys(values).reduce((acc, key) => {
    return {
      ...acc,
      [`${prefix}-${key}`]: values[key],
    }
  }, {} as Record<string, string>)
}
