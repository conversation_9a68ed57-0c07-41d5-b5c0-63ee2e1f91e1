export const populateTargetKeys = ({
  item,
  targetKeys,
  numberKeys,
  dateKeys,
  dateTimeKeys,
  dateRangeKeys,
  selectWithSearchKeys,
}) => {
  // Populate targetKeys with the name of the input. In case of dateRange, also populate with the
  // selected and inputMode, because they are part of the same field.
  if (item.type === "dateRange") {
    targetKeys.push(`${item.name}_selected`)
    targetKeys.push(`${item.name}_inputMode`)
  } else if (item.type === "selectWithSearch") {
    targetKeys.push(`${item.name}_value`)
    targetKeys.push(`${item.name}_searchValue`)
  } else {
    targetKeys.push(item.name)
  }

  // Populate the specific keys for each type of input for parsing and normalizing the values
  switch (item.type) {
    case "numeric": {
      if (!item?.inputProps?.isStringMode) {
        numberKeys.push(item.name)
      }
      break
    }
    case "date": {
      dateKeys.push(item.name)
      break
    }
    case "dateTime": {
      dateTimeKeys.push(item.name)
      break
    }
    case "dateRange": {
      dateRangeKeys.push(item.name)
      break
    }
    case "selectWithSearch": {
      selectWithSearchKeys.push(item.name)
      break
    }
    default: {
      break
    }
  }
}
