import React, {
  Children,
  cloneElement,
  ComponentType,
  createElement,
  forwardRef,
  memo,
  ReactElement,
  useCallback,
  useEffect,
  useMemo,
} from "react"
import uniqueId from "lodash/uniqueId"
import { createPortal } from "react-dom"

import { useOutsideClick, useToggle } from "hooks"
import { checkIsFunction, checkIsString, isTouchScreen } from "utils"
import { ReactElementWithRef } from "types"

import { StyledContentWrapper, StyledTriggerClone } from "./components"
import {
  useAdaptiveZIndex,
  usePopoverPlacement,
  useTriggerAndPopoverPositioning,
  useVisibilityChange,
} from "./hooks"
import {
  buildDataTriggerForPopoverIdAttribute,
  buildTriggerHandlerProps,
} from "./utils"
import {
  DATA_POPOVER_ID_ATTRIBUTE,
  GHOST_AREA_WIDTH_DEFAULT,
} from "./constants"

import { WithPopoverTriggerProps } from "./WithPopoverTriggerTypes"

export const withPopoverTrigger = <T extends object, R>(
  WrappedComponent: ComponentType<T>
) => {
  return memo(
    forwardRef<R, WithPopoverTriggerProps & T>(
      (
        {
          children,
          trigger: triggerProp = "hover",
          placement: placementProp,
          isVisible: isVisibleProp,
          isEmpty,
          isFixed,
          isContentWidthMatchedWithTrigger,
          isContentMinWidthMatchedWithTrigger,
          isOutsideClickListenerEnabled = true,
          offset,
          minimumCenterOffset = 0,
          minimumViewportClearance,
          placementsOrder,
          updateConditions,
          hasAutoAdjustment = true,
          onVisibilityChange,
          isGlobal = true,
          hasMaxZIndex = true,
          isHiddenOnObscure = true,
          nestedMenuKey,
          onTriggerObscured,
          ghostAreaWidth: ghostAreaWidthProp = GHOST_AREA_WIDTH_DEFAULT,
          onOutsideClick,
          ...props
        },
        ref
      ) => {
        const trigger = useMemo(() => {
          if (triggerProp === "hover") {
            return isTouchScreen() ? "click" : "hover"
          }

          return triggerProp
        }, [triggerProp])

        // Open/close popover
        const { open, handleOpen, handleClose } = useToggle(false)

        const isVisible =
          typeof isVisibleProp === "boolean" ? isVisibleProp : open

        // Track trigger and popover positions and dimensions
        const {
          triggerRef,
          popoverRef,
          triggerDimensions,
          popoverDimensions,
          triggerViewportRelativePositions,
          triggerCloneStyles,
        } = useTriggerAndPopoverPositioning({
          isVisible,
          updateConditions,
          isGlobal,
          onClose: handleClose,
          isHiddenOnObscure,
          onTriggerObscured,
        })

        const id = useMemo(() => uniqueId("p-"), [])

        useEffect(() => {
          const dataAttribute = buildDataTriggerForPopoverIdAttribute(id)

          triggerRef.current?.setAttribute(dataAttribute, "")
        }, [id])

        // Calculate placement
        const { placement, horizontalShift, verticalShift } =
          usePopoverPlacement({
            isVisible,
            isFixed,
            placement: placementProp,
            triggerDimensions,
            popoverDimensions,
            triggerViewportRelativePositions,
            offset,
            minimumViewportClearance,
            placementsOrder,
            hasAutoAdjustment,
          })

        const isVisibleProcessed: boolean =
          isVisible &&
          !!placement &&
          triggerDimensions.height > 0 &&
          triggerDimensions.width > 0 &&
          popoverDimensions.height > 0 &&
          popoverDimensions.width > 0

        // Call onVisibilityChange callback when popover shows/hides
        useVisibilityChange({
          isVisible: isVisibleProcessed,
          onVisibilityChange,
        })

        const handleOutsideClick = useCallback((): void => {
          if (checkIsFunction(onOutsideClick)) {
            onOutsideClick()
          }

          handleClose()
        }, [handleClose, onOutsideClick])

        // Track clicks outside trigger and popover boundaries
        useOutsideClick({
          refs: [triggerRef, popoverRef],
          isEnabled: isVisibleProcessed && isOutsideClickListenerEnabled,
          callback: handleOutsideClick,
          triggerEvent: "mousedown",
        })

        const zIndex = useAdaptiveZIndex({
          triggerRef,
          isGlobal,
          hasMaxZIndex,
        })

        const firstChild = checkIsString(children)
          ? createElement("span", {}, children)
          : (Children.only(children) as ReactElement)

        const {
          onPointerEnter,
          onPointerLeave,
          onClick,
          onBlur,
          onMouseDown,
          onKeyDown,
          ...triggerProps
        } = firstChild.props

        const triggerHandlerProps = buildTriggerHandlerProps({
          popoverRef,
          trigger,
          isVisible: isVisibleProcessed,
          onOpen: handleOpen,
          onClose: handleClose,
          onPointerEnter,
          onPointerLeave,
          onClick,
          onBlur,
          onMouseDown,
          onKeyDown,
        })

        const handlePopoverMouseLeave = useCallback((): void => {
          if (trigger === "hover") {
            handleClose()
          }
        }, [handleClose, trigger])

        const clonedRef = useCallback(
          (node): void => {
            const outerRef = (firstChild as ReactElementWithRef).ref

            // const isMutableObjectRef: boolean = outerRef && "current" in outerRef
            if (outerRef && "current" in outerRef) {
              outerRef.current = node
            }
            if (typeof outerRef === "function") {
              outerRef(node)
            }
            triggerRef.current = node
          },
          [firstChild]
        )

        const placementProcessed = placement || placementProp

        const visibilityProps = !checkIsString(firstChild.type)
          ? { isPopoverVisible: isVisible, placement: placementProcessed }
          : {}

        const portalNode = isGlobal
          ? document.body
          : triggerRef.current?.parentElement || document.body

        const ghostAreaWidth = trigger === "hover" ? ghostAreaWidthProp : 0

        return (
          <>
            {cloneElement(firstChild, {
              ...triggerProps,
              ...triggerHandlerProps,
              ...visibilityProps,
              ref: clonedRef,
            })}

            {isVisible
              ? createPortal(
                  <StyledTriggerClone
                    data-z-index={zIndex}
                    isEmpty={isEmpty}
                    style={triggerCloneStyles}
                    zIndex={zIndex}
                    {...{
                      [DATA_POPOVER_ID_ATTRIBUTE]: id,
                    }}
                  >
                    <StyledContentWrapper
                      ref={popoverRef}
                      data-nested-menu-id={nestedMenuKey}
                      data-placement={placementProcessed}
                      ghostAreaWidth={ghostAreaWidth}
                      height={popoverDimensions.height}
                      horizontalShift={horizontalShift}
                      isVisible={isVisibleProcessed}
                      minimumCenterOffset={minimumCenterOffset}
                      minimumViewportClearance={minimumViewportClearance}
                      offset={offset}
                      placement={placementProcessed}
                      triggerDimensions={triggerDimensions}
                      verticalShift={verticalShift}
                      width={popoverDimensions.width}
                      isContentMinWidthMatchedWithTrigger={
                        isContentMinWidthMatchedWithTrigger
                      }
                      isContentWidthMatchedWithTrigger={
                        isContentWidthMatchedWithTrigger
                      }
                      onPointerLeave={handlePopoverMouseLeave}
                    >
                      <WrappedComponent
                        ref={ref}
                        {...(props as T)}
                        horizontalShift={horizontalShift}
                        isVisible={isVisible}
                        placement={placementProcessed}
                        verticalShift={verticalShift}
                        isContentWidthMatchedWithTrigger={
                          isContentWidthMatchedWithTrigger
                        }
                        onClose={handleClose}
                      />
                    </StyledContentWrapper>
                  </StyledTriggerClone>,
                  portalNode
                )
              : null}
          </>
        )
      }
    )
  )
}
