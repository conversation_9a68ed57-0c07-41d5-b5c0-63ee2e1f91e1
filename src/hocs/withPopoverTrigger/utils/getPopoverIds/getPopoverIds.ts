import {
  DATA_CHILD_OF_POPOVER_ID_ATTRIBUTE,
  DATA_TRIGGER_FOR_POPOVER_ID_ATTRIBUTE,
} from "../../constants"

import { GetPopoverIds } from "./GetPopoverIdsTypes"

export const getPopoverIds: GetPopoverIds = ({ element, type }) => {
  const popoverIds: Array<string> = []

  const matchStringRaw =
    type === "triggerFor"
      ? DATA_TRIGGER_FOR_POPOVER_ID_ATTRIBUTE
      : DATA_CHILD_OF_POPOVER_ID_ATTRIBUTE

  const matchString = matchStringRaw.replace("{id}", "")

  element.getAttributeNames().forEach((key) => {
    if (!key.startsWith(matchString)) {
      return
    }

    const popoverId = key.replace(matchString, "")

    popoverIds.push(popoverId)
  })

  return popoverIds
}
