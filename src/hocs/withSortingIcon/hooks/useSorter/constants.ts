import { IconNames } from "components/Icon/IconTypes"
import { SortValue } from "hocs/withSortingIcon/WithSortingIconTypes"

export const SORT_VALUES: Record<SortValue, SortValue> = {
  asc: "asc",
  desc: "desc",
}

export const OPTIONS: Array<{
  value: SortValue
  iconLeft: IconNames
}> = [
  {
    value: SORT_VALUES.asc,
    iconLeft: "icnSortAscending",
  },
  {
    value: SORT_VALUES.desc,
    iconLeft: "icnSortDescending",
  },
]
