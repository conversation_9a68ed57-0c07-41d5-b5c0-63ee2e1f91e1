:root {
  //color text
  --color-text-main: #000000;
  --color-text-second: #666666;
  --color-text-placeholders: #bdbdbd;
  --color-text-disable: #c8cddb;
  --color-text-white: #ffffff;
  --color-text-link: #0055cc;
  --color-text-link-disable: #ccddf5;
  --color-text-done: #8bc34a;
  --color-text-error: #eb5757;
  --color-text-error-disable: #eb5757;
  --color-text-warning: #ffaa00;

  //icon color
  --color-icon-clickable: #666666;
  --color-icon-static: #bdbdbd;
  --color-icon-static-2: #ccddf5;
  --color-icon-disable: #c8cddb;
  --color-icon-disable-2: #0f3775;
  --color-icon-white: #ffffff;
  --color-icon-active: #0055cc;
  --color-icon-warning: #ffaa00;
  --color-icon-error: #f89191;
  --color-icon-done: #8bc34a;

  //Service elements
  --color-border-main: #d5dce0;
  --color-grid-hover: #ebedf0;
  --color-background-second: #f6f8f9;
  --color-background-disable: #f6f6f9;
  --color-background-black: #000000;
  --color-row-select: #e5eefa;
  --color-row-select-hover: #ccddf5;
  --color-select-active: #0055cc;
  --color-main-background: #ffffff;
  --color-menu-background: #f5f5f5;
  --color-background-blue: #0055cc;
  --color-background-orange: #ffeecc;
  --color-background-green: #e8f3db;

  //Interactive elements
  --color-int-on-active: #0055cc;
  --color-int-off-active: #c8cddb;
  --color-int-on-disable: #ccddf5;
  --color-int-off-disable: #ebedf0;
  --color-int-background-second: #ffffff;

  //Badges
  --color-badge-1: #0055cc;
  --color-badge-2: #eb5757;
  --color-badge-3: #00a646;
  --color-badge-4: #8bc34a;
  --color-badge-5: #ffaa00;
  --color-badge-6: #d5dce0;
  --color-badge-background: #ffffff;
  --badge-size-l: 32px;
  --badge-size-m: 20px;
  --badge-size-s: 14px;
  --badge-size-xs: 5px;
  --border-white: 1px solid var(--color-main-background);

  //Buttons
  --color-button-primary: #0055cc;
  --color-button-secondary: #ffffff;
  --color-button-border-primary: #0055cc;
  --color-button-border-secondary: #d5dce0;
  --color-button-disable: #f6f6f9;
  //delete this after refactor button start
  --button-font-size-s: 14px; //delete
  --button-font-size-m: 16px; //delete
  --button-font-size-xl: 22px; //delete
  --button-icon-size-m: 32px; //delete
  --button-icon-size-l: 40px; //delete
  --padding-button-main: 6px; //delete
  --padding-button-large: 10px; //delete
  // finish

  // Skeleton
  --color-skeleton-background: #ebedf0;
  --color-skeleton-highlight: #f6f8f9;

  //Dropdown
  --dropdown-item-height-m: 32px;

  //Calendar
  --calendar-cell-height-m: 32px;
  --calendar-cell-width-m: 32px;
  --calendar-month-width-m: 78px;

  //Alerts
  --color-alert-background-info: #e5eefa;
  --color-alert-background-warning: #ffeecc;
  --color-alert-background-error: #fee9e9;
  --color-alert-background-done: #e8f3db;
  --color-alert-background-default: #f6f6f9;

  //Checkbox
  --color-checkbox-border: #d5dce0;
  --color-checkbox-checked: #0055cc;
  --color-checkbox-background-disabled: #ebedf0;
  --checkbox-size-m: 16px;
  --checkbox-size-l: 24px;
  --checkbox-size-xl: 32px;

  //Switch
  --switcher-height-m: 22px;
  --switcher-height-s: 16px;
  --switcher-width-m: 40px;
  --switcher-width-s: 28px;
  --switcher-mark-size-m: 18px;
  --switcher-mark-size-s: 12px;
  --switcher-spinner-size-xs: 10px;
  --switcher-padding-xs: 2px;

  //Radio
  --radio-size-m: 16px;
  --radio-mark-size-m: 8px;

  //Fonts
  --font-family-main: Roboto, sans-serif;
  --font-weight-regular: 400;
  --font-weight-medium: 500;
  --font-weight-bold: 700;
  --font-style-main: normal;
  --font-stretch-main: normal;
  --line-height-main: normal;
  --letter-spacing-main: normal;
  --font-default-styles-s: var(--font-style-main) var(--font-weight-regular)
    var(--font-stretch-main) var(--font-family-main);
  --font-default-styles-m: var(--font-style-main) var(--font-weight-medium)
    var(--font-stretch-main) var(--font-family-main);
  --font-default-styles-l: var(--font-style-main) var(--font-weight-bold)
    var(--font-stretch-main) var(--font-family-main);

  --font-headline-1: var(--font-weight-bold) 36px/50px var(--font-family-main);
  --font-headline-2: var(--font-weight-bold) 24px/32px var(--font-family-main);
  --font-headline-3: var(--font-weight-bold) 18px/26px var(--font-family-main);
  --font-headline-4: var(--font-weight-regular) 18px/26px
    var(--font-family-main);
  --font-headline-5: var(--font-weight-bold) 16px/22px var(--font-family-main);

  --font-service-text-1: var(--font-weight-regular) 12px/12px
    var(--font-family-main);
  --font-service-text-2: var(--font-weight-medium) 48px/48px
    var(--font-family-main);

  --font-body-text-1: var(--font-weight-regular) 16px/24px
    var(--font-family-main);
  --font-body-text-2: var(--font-weight-bold) 14px/20px var(--font-family-main);
  --font-body-text-3: var(--font-weight-medium) 14px/20px
    var(--font-family-main);
  --font-body-text-4: var(--font-weight-regular) 14px/20px
    var(--font-family-main);
  --font-body-text-5: var(--font-weight-bold) 13px/18px var(--font-family-main);
  --font-body-text-6: var(--font-weight-medium) 13px/18px
    var(--font-family-main);
  --font-body-text-7: var(--font-weight-regular) 13px/18px
    var(--font-family-main);
  --font-body-text-8: var(--font-weight-bold) 12px/18px var(--font-family-main);
  --font-body-text-9: var(--font-weight-regular) 12px/18px
    var(--font-family-main);
  --font-body-text-10: var(--font-weight-regular) 11px/12px
    var(--font-family-main);
  --font-body-text-11: var(--font-weight-regular) 10px/10px
    var(--font-family-main);

  //Media
  --media-desktop-xl: 2560px;
  --media-desktop-l: 1920px;
  --media-desktop-m: 1366px;
  --media-desktop-s: 1024px;
  --media-tablet: 768px;
  --media-mobile-l: 480px;
  --media-mobile-m: 375px;
  --media-mobile-s: 320px;

  //Blocks
  --block-size-xl: 1880px;
  --block-size-l: 1326px;
  --block-size-m: 984px;
  --block-size-s: 728px;
  --block-size-xs: 300px;

  //Modals
  --modal-size-xl: 1880px;
  --modal-size-l: 984px;
  --modal-size-m: 728px;
  --modal-size-s: 440px;
  --modal-size-xs: 300px;
  --modal-height-s: 50px;
  --modal-height-m: 52px;

  // Border colors
  --color-border-active: #0055cc;
  --color-border-white: #ffffff;
  --color-border-black: #000000;
  --color-border-light-blue: #ccddf5;
  --color-border-error: #f89191;
  --color-border-done: #8bc34a;

  // Borders
  --border-main: 1px solid var(--color-border-main);
  --border-error: 1px solid var(--color-border-error);
  --border-done: 1px solid var(--color-border-done);
  --border-checkbox: 1px solid var(--color-checkbox-border);

  // Button borders
  --border-button-primary: 1px solid var(--color-button-border-primary);
  --border-button-secondary: 1px solid var(--color-button-border-secondary);

  // Border radii
  --border-radius: 2px;
  --border-radius-circle: 50%;
  --border-radius-rounded: 2000px;

  //Margins
  --grid-margin: 7px;
  --margin-xl: 40px;
  --margin-l: 20px;
  --margin-m: 10px;
  --margin-s: 5px;

  // Paddings
  --padding-xl: 40px;
  --padding-l: 20px;
  --padding-m: 10px;
  --padding-s: 5px;

  // Gaps
  --gap-xl: 40px;
  --gap-l: 20px;
  --gap-m: 10px;
  --gap-s: 5px;

  // Default height
  --input-height-medium: 32px;
  --input-height-big: 40px;

  --button-height-small: 32px;
  --button-height-large: 40px;

  //Icon sizes
  --icon-size-1: 12px;
  --icon-size-2: 14px;
  --icon-size-3: 16px;
  --icon-size-4: 18px;
  --icon-size-5: 20px;
  --icon-size-6: 24px;
  --icon-size-7: 32px;
  --icon-size-8: 40px;
  --icon-size-9: 48px;
  --icon-size-10: 56px;
  --icon-size-11: 64px;

  //Box-shadow
  --box-shadow: 0px 1px 8px rgba(0, 0, 0, 0.3);
  --button-box-shadow-focus: 0 0 0 3px rgba(0, 85, 204, 0.1);
  --input-box-shadow-focus: 0 0 0 3px rgba(0, 85, 204, 0.1);
  --box-shadow-modal: 0 0 8px 0 rgba(21, 27, 38, 0.15);
  --box-shadow-switch: 0 1px 3px 0 rgba(21, 27, 38, 0.4);

  //Opacity
  --box-opacity-0: 0;
  --box-opacity-1: 0.1;
  --box-opacity-2: 0.2;
  --box-opacity-3: 0.3;
  --box-opacity-4: 0.4;
  --box-opacity-5: 0.5;
  --box-opacity-6: 0.6;
  --box-opacity-7: 0.7;
  --box-opacity-8: 0.8;
  --box-opacity-9: 0.9;
  --box-opacity-10: 1;

  // Transitions
  --default-button-transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  --default-button-transition-properties: background-color, color, border,
    box-shadow, fill;

  //Tags
  --tag-height-m: 32px;
  --tag-height-s: 24px;
  --tag-height-xs: 10px;
  --tag-width-xs: 10px;
  --color-tag-hover: #0055cc;
  --color-tag-background: #ffffff;
  --color-tag-border: #d5dce0;
  --color-picker-width-m: 24px;

  //Statuses
  --color-status-1: #0055cc;
  --color-status-2: #0f3775;
  --color-status-3: #eb5757;
  --color-status-4: #d70606;
  --color-status-5: #00a646;
  --color-status-6: #8bc34a;
  --color-status-7: #ffaa00;
  --color-status-8: #ec7100;
  --color-status-9: #666666;
  --color-status-10: #bdbdbd;

  //Menu
  --menu-size-m: 40px;

  // Upload Colors
  --color-file-background-load: #e5eefa;
  --color-file-background-warning: #ffeecc;
  --color-file-background-error: #fee9e9;
  --color-file-background-done: #e8f3db;

  // Image
  --container-image-size-160: 160px;
  --container-image-size-140: 140px;
  --container-image-size-80: 80px;
  --container-image-size-64: 64px;
  --container-image-size-48: 48px;
  --container-image-size-40: 40px;
  --container-image-size-32: 32px;
  --container-image-size-24: 24px;

  // Avatar
  --border-avatar-blue: 2px solid var(--color-border-active);
  --border-avatar-red: 2px solid var(--color-border-error);
  --border-avatar-white: 2px solid var(--color-border-white);

  // Table
  --grid-row-size-m: 64px;
  --grid-row-size-s: 52px;

  // Tree
  --tree-item-height: 38px;

  // Side menu (aka Sidebar)
  --side-menu-size-m: 60px;

  // Slider
  --slider-size-l: 24px;
  --slider-size-m: 16px;
  --slider-size-s: 10px;
  --slider-size-xs: 5px;
  --item-box-shadow-focus: 0 0 0 3px rgba(0, 85, 204, 0.1);

  // Drawer
  --drawer-size-xl: 1880px;
  --drawer-size-l: 984px;
  --drawer-size-m: 728px;
  --drawer-size-s: 440px;
  --drawer-size-xs: 300px;
  --drawer-height-s: 50px;
  --drawer-height-m: 52px;
}
