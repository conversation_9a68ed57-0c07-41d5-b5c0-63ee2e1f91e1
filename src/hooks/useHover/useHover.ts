import { useState } from "react"

import { UseHover } from "./UseHoverTypes"

export const useHover: UseHover = (initialValue) => {
  const [isHovered, setIsHovered] = useState(initialValue ?? false)

  const handlePointerEnter = () => {
    setIsHovered(true)
  }

  const handlePointerLeave = () => {
    setIsHovered(false)
  }

  return {
    isHovered,
    handlePointerEnter,
    handlePointerLeave,
  }
}
