import { useEffect, useRef } from "react"
import isEqual from "lodash.isequal"

import { UseUpdateIfChangedParams } from "./UseUpdateIfChangedTypes"

export const useUpdateIfChanged = <Type>({
  values,
  updateValues,
}: UseUpdateIfChangedParams<Type>) => {
  const valuesPrev = useRef<Type>()

  useEffect(() => {
    if (isEqual(valuesPrev.current, values)) {
      return
    }

    valuesPrev.current = values
    updateValues(values)
  }, [values, updateValues])
}
