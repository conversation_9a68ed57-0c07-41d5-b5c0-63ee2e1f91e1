import type { InputWithDropdownContainerProps } from "components/InputWithDropdownContainer/InputWithDropdownContainerTypes"
import type { BulkChangeCommonParams } from "types/BulkChangeCommonParams"

export type UseBulkChangeInputModesParams<Type> = BulkChangeCommonParams & {
  defaultValue?: Type
  value?: Type
  displayedValue?: string
  onChange?: (value: Type) => void
} & Pick<InputWithDropdownContainerProps, "selectedModeValue" | "onSelectMode">
