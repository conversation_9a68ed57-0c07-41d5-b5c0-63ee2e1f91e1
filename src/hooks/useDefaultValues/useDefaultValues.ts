import { useEffect, useState } from "react"

import { buildDefaultValues } from "utils"

import {
  UseDefaultValuesParams,
  UseDefaultValuesReturn,
} from "./UseDefaultValuesTypes"

export const useDefaultValues = <DataType>({
  fields,
}: UseDefaultValuesParams): UseDefaultValuesReturn<DataType> => {
  const [defaultValues, setDefaultValues] = useState<
    UseDefaultValuesReturn<DataType>
  >({})

  useEffect(() => {
    const defaultValuesNew = buildDefaultValues(fields)

    const isChanged =
      JSON.stringify(defaultValuesNew) !== JSON.stringify(defaultValues)

    if (isChanged) {
      setDefaultValues(defaultValuesNew)
    }
  }, [fields])

  return defaultValues
}
