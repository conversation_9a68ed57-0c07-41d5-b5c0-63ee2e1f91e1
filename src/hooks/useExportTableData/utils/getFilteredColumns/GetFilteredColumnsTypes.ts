import type { TableBaseData } from "components/Table/TableTypes"

import type { UseExportTableDataParams } from "../../UseExportTableDataTypes"

export type GetFilteredColumnsParams<DataType extends TableBaseData> = Pick<
  UseExportTableDataParams<DataType>,
  "columns" | "settings" | "rowSpanMax" | "hasIndexColumn"
>

export type GetFilteredColumnsReturn<DataType extends TableBaseData> = {
  titleRow: Array<string>
} & Pick<UseExportTableDataParams<DataType>, "columns" | "rowSpanMax">
