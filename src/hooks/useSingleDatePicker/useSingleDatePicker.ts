import React, { useCallback, useState } from "react"

import { checkIsFunction, getCurrentTimezone } from "utils"

import { UseSingleDatePicker } from "./UseSingleDatePickerTypes"

export const useSingleDatePicker: UseSingleDatePicker = ({
  selected: selectedProp,
  onSelect: onSelectProp,
  timezone: timezoneProp,
  isClosedOnSelect,
  onClose,
  onOpen,
  onMouseDown,
}) => {
  const [selected, setSelected] = useState<Date>()

  const timezone = timezoneProp || getCurrentTimezone()

  const selectedProcessed = selectedProp !== undefined ? selectedProp : selected

  const handleSelect = useCallback(
    (selectedNew) => {
      if (isClosedOnSelect) {
        onClose()
      }

      if (selectedProp === undefined) {
        setSelected(selectedNew)
      }
      if (!checkIsFunction(onSelectProp)) {
        return
      }
      if (selectedNew === null) {
        onSelectProp({
          selected: null,
        })

        return
      }

      onSelectProp({
        selected: selectedNew,
      })
    },
    [onSelectProp, selectedProcessed, selectedProp, timezone]
  )

  const handleMouseDown = (event: React.MouseEvent<HTMLDivElement>): void => {
    onOpen()

    if (checkIsFunction(onMouseDown)) {
      onMouseDown(event)
    }
  }

  return {
    timezone,
    selected: selectedProcessed,
    onSelect: handleSelect,
    onMouseDown: handleMouseDown,
  }
}
