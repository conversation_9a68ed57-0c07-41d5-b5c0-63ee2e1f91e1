import isEqual from "lodash.isequal"

import { UpdateFilterStatesParams } from "hooks/useFilters/utils/updateFilterStates/UpdateFilterStatesTypes"
import { checkIsArray } from "utils"
import { COMMON_FILTER_KEYS } from "consts"

export const updateFilterStates = <FilterValuesType>({
  params,
  hiddenKeys,
  persistOnClearKeys,
  setFilterStates,
  resetFiltersWatchList,
}: UpdateFilterStatesParams<FilterValuesType>) => {
  let hasActiveFilters: boolean = false
  let hasActiveHiddenFilters: boolean = false
  const activeFilters = new Set<string>()
  const hasWatchList = checkIsArray(resetFiltersWatchList)

  Object.keys(params).forEach((key) => {
    const isKeyActiveFilter: boolean =
      !!params[key] &&
      !COMMON_FILTER_KEYS[key] &&
      !persistOnClearKeys.includes(key)

    const isKeyActiveAndWatchedFilter: boolean = hasWatchList
      ? resetFiltersWatchList.includes(key) && isKeyActiveFilter
      : isKeyActiveFilter

    hasActiveFilters = hasActiveFilters || isKeyActiveAndWatchedFilter

    if (isKeyActiveAndWatchedFilter) {
      hasActiveHiddenFilters = hiddenKeys.has(key)
      activeFilters.add(key)
    }
  })

  setFilterStates((prevState) => {
    const newState = {
      hasActiveFilters,
      hasActiveHiddenFilters,
      activeFilters,
    }

    if (isEqual(prevState, newState)) {
      return prevState
    }

    return newState
  })
}
