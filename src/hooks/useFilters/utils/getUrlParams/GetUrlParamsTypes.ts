import { UseFiltersParams } from "hooks/useFilters/UseFiltersTypes"
import { FilterPaginationValues, FilterSortValues } from "types"

export type GetUrlParamsParams<
  FilterValuesType extends FilterSortValues & FilterPaginationValues
> = Pick<UseFiltersParams<FilterValuesType>, "prefix" | "targetKeys"> & {
  urlSearch?: string
}

export type GetUrlParamsReturn = {
  targetParams: Record<string, string>
  restParams: Record<string, string>
}
