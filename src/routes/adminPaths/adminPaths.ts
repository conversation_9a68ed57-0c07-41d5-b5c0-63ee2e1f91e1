import { SHORT_PATHS } from "routes/shortPaths"

const { PATH_ADMIN } = SHORT_PATHS

export const COMMON_ADMIN_ROUTES = {
  PATH_PREFIX_PAYMENTS: `${PATH_ADMIN}/payments`,
} as const

const { PATH_PREFIX_PAYMENTS } = COMMON_ADMIN_ROUTES

export const ADDITIONAL_ADMIN_ROUTES = {
  PATH_TARIFF_CONFIGURATOR: `${PATH_PREFIX_PAYMENTS}/paymentFlexible`,
  PATH_TRANSACTION_WITHOUT_ID: `${PATH_PREFIX_PAYMENTS}/customerInvoice/transaction`,
  PATH_TRANSLATION_SINGLE: `${PATH_ADMIN}/translationEdit`,
  PATH_PARTNERS_CHANNEL: `${PATH_ADMIN}/partner/channel`,
  PATH_MANAGE_PAYMENT_MODULE_VERSIONS: `${PATH_PREFIX_PAYMENTS}/paymentModuleVersion`,
  PATH_ADVERTISING_BANNER_PREVIEW_SINGLE: `${PATH_ADMIN}/bannerPreview`,
} as const

const {
  PATH_TARIFF_CONFIGURATOR,
  PATH_TRANSACTION_WITHOUT_ID,
  PATH_TRANSLATION_SINGLE,
  PATH_PARTNERS_CHANNEL,
  PATH_MANAGE_PAYMENT_MODULE_VERSIONS,
  PATH_ADVERTISING_BANNER_PREVIEW_SINGLE,
} = ADDITIONAL_ADMIN_ROUTES

export const ADMIN_ROUTES = {
  PATH_CUSTOMERS: `${PATH_ADMIN}/customer`,
  PATH_USERS: `${PATH_ADMIN}/user`,
  PATH_ROLES: `${PATH_ADMIN}/rbacRole`,
  PATH_FINANCIAL_OVERVIEW: `${PATH_PREFIX_PAYMENTS}/customerInvoiceSummary`,
  PATH_CUSTOMER_INVOICES: `${PATH_PREFIX_PAYMENTS}/customerInvoice`,
  PATH_CUSTOMER_INVOICE_STATUS: `${PATH_PREFIX_PAYMENTS}/invoiceStatus`,
  PATH_CUSTOMER_INVOICE_TRANSACTIONS: `${PATH_PREFIX_PAYMENTS}/customerInvoiceTransaction`,
  PATH_SUBSCRIPTION_SETTINGS: `${PATH_PREFIX_PAYMENTS}/paymentModule`,
  PATH_ADMIN_DASHBOARD: `${PATH_ADMIN}/adminDashboard`,
  PATH_REPRICER_REVENUE_PREVIEW: `${PATH_ADMIN}/repricerRevenuePreview`,
  PATH_BUSINESS_ANALYTICS_REVENUE_PREVIEW: `${PATH_ADMIN}/businessAnalyticsRevenuePreview`,
  PATH_MONTHLY_INVOICE_SUMMARY: `${PATH_PREFIX_PAYMENTS}/monthlyInvoiceSummary`,
  PATH_TRANSACTION_STATISTICS: `${PATH_ADMIN}/transactionStatistics`,
  PATH_EMAIL_TEMPLATES: `${PATH_ADMIN}/mailerTemplate`,
  PATH_EMAIL_QUEUE: `${PATH_ADMIN}/mailerQueue`,
  PATH_AMAZON_REGIONS: `${PATH_ADMIN}/amazonZone`,
  PATH_AMAZON_MARKETPLACES: `${PATH_ADMIN}/amazonMarketplace`,
  PATH_PRODUCT_ACTIVITIES: `${PATH_ADMIN}/productActivity`,
  PATH_AMAZON_CASE_TEMPLATES: `${PATH_ADMIN}/templates`,
  PATH_AMAZON_LINKS: `${PATH_ADMIN}/amazonLinks`,
  PATH_LANGUAGE_SETTINGS: `${PATH_ADMIN}/language`,
  PATH_COUNTRIES: `${PATH_ADMIN}/country`,
  PATH_TRANSLATION: `${PATH_ADMIN}/translation`,
  PATH_PARTNERS: `${PATH_ADMIN}/partner`,
  PATH_PARTNER_CREDIT_NOTES: `${PATH_ADMIN}/partnerPayments`,
  PATH_ADVERTISING_OR_BANNERS: `${PATH_ADMIN}/advertising`,
  PATH_ADVERTISING_BANNER_PREVIEW: `${PATH_ADVERTISING_BANNER_PREVIEW_SINGLE}/:bannerId`,
  PATH_NAVIGATION_MANAGEMENT: `${PATH_ADMIN}/navigationManagement`,
  PATH_AUTH_CLIENT_OR_MANAGE_AUTH_CLIENTS: `${PATH_ADMIN}/authClients`,
  PATH_ACTIVITY_LOG: `${PATH_ADMIN}/changeHistory`,
  PATH_CASE_STATISTICS: `${PATH_ADMIN}/caseStatistics`,
  PATH_TRANSACTION: `${PATH_TRANSACTION_WITHOUT_ID}/:transactionId`,
  PATH_TRANSLATION_SINGLE_ID: `${PATH_TRANSLATION_SINGLE}/:translationId`,
  PATH_PARTNERS_CHANNEL_ID: `${PATH_PARTNERS_CHANNEL}/:partnerId`,
  PATH_TARIFF_CONFIGURATOR_ID: `${PATH_TARIFF_CONFIGURATOR}/:paymentModuleId`,
  PATH_MANAGE_PAYMENT_MODULE_VERSIONS_ID: `${PATH_MANAGE_PAYMENT_MODULE_VERSIONS}/:paymentModuleId`,
  PATH_MISSED_TRANSLATIONS: `${PATH_ADMIN}/missedTranslations`,
  PATH_LOST_ALL_CASES: `${PATH_ADMIN}/lost-all-cases`,
  PATH_LOST_ALL_CASES_BULK_MANAGEMENT: `${PATH_ADMIN}/lost-all-cases-bulk-management`,
  PATH_AMAZON_SELLER_CENTRAL_ACCOUNTS: `${PATH_ADMIN}/lost-accounts`,
  PATH_VAT_SETTINGS: `${PATH_ADMIN}/VATSettingsAdmin`,
  PATH_SELLER_CENTRAL_INVITATIONS: `${PATH_ADMIN}/lost-invitation`,
  PATH_PROMOCODES: `${PATH_ADMIN}/promocodes`,
  PATH_GLOBAL_AMAZON_ACCOUNTS: `${PATH_ADMIN}/globalAmazonAccounts`,
} as const
